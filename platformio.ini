; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:genericSTM32F103C8]
platform = ststm32
board = genericSTM32F103C8
framework = arduino
; 确保板级定义正确
board_build.f_cpu = 72000000L
build_flags =
    -DHSE_VALUE=8000000
    -DHAL_UART_MODULE_ENABLED
    -DSERIAL_UART_INSTANCE=1
monitor_speed = 115200
monitor_port = COM*
upload_protocol = jlink
debug_tool = jlink
lib_deps =
    koendv/RTT Stream@^1.4.1
