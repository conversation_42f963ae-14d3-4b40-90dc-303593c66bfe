/**
 * @file taskF.cpp
 * @brief 任务调度器模块实现文件
 * @details 实现非阻塞的任务调度功能
 */

#include "taskF.h"
#include "logF.h"
#include "Arduino.h"
#include <string.h>

// 内部状态变量
static Task g_taskList[MAX_TASKS];
static uint8_t g_taskCount = 0;
static bool g_isRunning = false;
static uint32_t g_totalRuns = 0;

// 全局状态结构体
const TaskSchedulerInfo taskInfo = {
    .taskCount = g_taskCount,
    .isRunning = g_isRunning,
    .totalRuns = g_totalRuns
};

/**
 * @brief 查找任务索引
 * @param taskName 任务名称
 * @return 任务索引，-1表示未找到
 */
static int findTaskIndex(const char* taskName)
{
    if (!taskName) return -1;

    for (uint8_t i = 0; i < g_taskCount; i++) {
        if (strcmp(g_taskList[i].name, taskName) == 0) {
            return i;
        }
    }
    return -1;
}



/**
 * @brief 注册新任务
 */
bool taskRegister(const char* name, uint32_t interval, void (*function)(), bool enabled)
{
    // 检查参数有效性
    if (!name || !function || g_taskCount >= MAX_TASKS) {
        // 移除LOG_E调用，避免递归问题
        return false;
    }

    // 检查任务名是否已存在
    if (findTaskIndex(name) >= 0) {
        // 移除LOG_E调用，避免递归问题
        return false;
    }

    // 注册新任务
    Task* task = &g_taskList[g_taskCount];
    task->name = name;
    task->interval = interval;
    task->function = function;
    task->enabled = enabled;
    task->lastRun = millis();

    g_taskCount++;

    LOG_I("TASK", "任务注册成功: %s (间隔: %lu ms, 状态: %s)",
          name, interval, enabled ? "启用" : "禁用");

    return true;
}

/**
 * @brief 运行任务调度器
 */
void taskScheduler()
{
    if (g_taskCount == 0) {
        return;  // 没有任务需要调度
    }

    g_isRunning = true;
    uint32_t currentTime = millis();

    for (uint8_t i = 0; i < g_taskCount; i++) {
        Task* task = &g_taskList[i];

        // 检查任务是否启用且到达执行时间
        if (task->enabled && (currentTime - task->lastRun >= task->interval)) {
            task->lastRun = currentTime;
            g_totalRuns++;

            // 执行任务
            if (task->function) {
                task->function();
            }
        }
    }

    g_isRunning = false;
}

/**
 * @brief 启用或禁用任务
 */
bool taskEnable(const char* taskName, bool enable)
{
    int index = findTaskIndex(taskName);
    if (index < 0) {
        // 移除LOG_E调用，避免递归问题
        return false;
    }

    g_taskList[index].enabled = enable;
    // 移除LOG_I调用，避免递归问题
    return true;
}

/**
 * @brief 设置任务执行间隔
 */
bool taskSetInterval(const char* taskName, uint32_t newInterval)
{
    int index = findTaskIndex(taskName);
    if (index < 0) {
        // 移除LOG_E调用，避免递归问题
        return false;
    }

    g_taskList[index].interval = newInterval;
    // 移除LOG_I调用，避免递归问题
    return true;
}

/**
 * @brief 获取任务信息
 */
bool taskGetInfo(const char* taskName, Task* task)
{
    if (!task) return false;

    int index = findTaskIndex(taskName);
    if (index < 0) {
        return false;
    }

    *task = g_taskList[index];
    return true;
}

/**
 * @brief 列出所有任务
 */
void taskListAll()
{
    // 移除所有LOG_I调用，避免递归问题
    // 这个函数暂时禁用，避免在任务调度器中调用LOG
}

/**
 * @brief 获取调度器统计信息
 */
const TaskSchedulerInfo& taskGetSchedulerInfo()
{
    return taskInfo;
}