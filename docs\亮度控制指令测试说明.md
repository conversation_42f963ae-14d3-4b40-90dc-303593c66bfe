# CH423S 亮度控制指令测试说明

## 🎯 **功能概述**

通过串口和RTT发送JSON格式指令，实时控制CH423S的LED亮度级别。
支持4个亮度级别：低、中、高、最高。

## 📝 **指令格式**

### 亮度设置指令
```json
{"cmd":"brightness","level":"0"}    // 设置低亮度
{"cmd":"brightness","level":"1"}    // 设置中等亮度
{"cmd":"brightness","level":"2"}    // 设置高亮度
{"cmd":"brightness","level":"3"}    // 设置最高亮度
```

### 亮度查询指令
```json
{"cmd":"brightness","query":"1"}    // 查询当前亮度级别
```

### 亮度测试指令
```json
{"cmd":"brightness","test":"1"}     // 执行亮度测试（依次测试所有级别）
```

## 🚀 **测试步骤**

### 通过串口测试

1. **连接串口**
   - 波特率：115200
   - 数据位：8
   - 停止位：1
   - 校验位：无

2. **发送测试指令**
   ```json
   {"cmd":"brightness","level":"0"}
   {"cmd":"brightness","level":"1"}
   {"cmd":"brightness","level":"2"}
   {"cmd":"brightness","level":"3"}
   ```

3. **查询当前亮度**
   ```json
   {"cmd":"brightness","query":"1"}
   ```

4. **执行亮度测试**
   ```json
   {"cmd":"brightness","test":"1"}
   ```

### 通过RTT测试

1. **连接RTT Viewer**
   - 连接STM32设备
   - 打开RTT Viewer

2. **发送测试指令**（在RTT输入框中输入）
   ```json
   {"cmd":"brightness","level":"2"}
   {"cmd":"brightness","query":"1"}
   {"cmd":"brightness","test":"1"}
   ```

## 📊 **预期结果**

### 成功日志示例
```
[12345] INFO [HUB] 亮度设置成功: 级别2
[12346] INFO [HUB] 当前亮度级别: 2
[12347] INFO [HUB] 开始执行亮度测试...
[12348] INFO [CH423S] 开始亮度测试...
[12349] INFO [CH423S] 测试亮度级别 0
[15349] INFO [CH423S] 测试亮度级别 1
[18349] INFO [CH423S] 测试亮度级别 2
[21349] INFO [CH423S] 测试亮度级别 3
[24349] INFO [CH423S] 亮度测试完成，已设置为最高亮度
[24350] INFO [HUB] 亮度测试完成
```

### 错误日志示例
```
[12345] WARN [HUB] 无效的亮度级别: 5
[12346] ERROR [HUB] 亮度设置失败: 级别0
[12347] WARN [HUB] 亮度指令中未找到有效的参数: {"cmd":"brightness"}
```

## 🔧 **技术实现**

### 架构流程
```
串口/RTT → uartF/logF → hubF → ch423sF
```

### 关键函数
- `hubProcessCommand()` - 指令分发
- `processBrightnessCommand()` - 亮度指令处理
- `ch423sSetBrightness()` - 硬件亮度设置
- `ch423sGetBrightness()` - 亮度查询
- `ch423sTestBrightness()` - 亮度测试

## 💡 **使用技巧**

1. **快速测试**: 先发送 `{"cmd":"brightness","level":"0"}` 设置最低亮度，再发送 `{"cmd":"brightness","level":"3"}` 设置最高亮度，观察亮度变化
2. **状态确认**: 每次设置后发送查询指令确认当前状态
3. **完整测试**: 使用测试指令观察所有亮度级别的效果
4. **组合使用**: 可以与LED控制指令组合使用，先设置亮度再控制LED

## 🚨 **注意事项**

1. **参数范围**: 亮度级别只支持 0-3，超出范围会报错
2. **测试时间**: 亮度测试会持续约12秒（每级别3秒），期间系统会暂停响应
3. **硬件依赖**: 需要CH423S硬件支持，如果硬件未正确连接可能设置失败
4. **指令格式**: 必须严格按照JSON格式，缺少引号或括号会导致解析失败

## 🔍 **故障排除**

### 常见问题

1. **指令无响应**
   - 检查JSON格式是否正确
   - 确认串口/RTT连接正常
   - 查看日志输出确认指令是否被接收

2. **亮度设置失败**
   - 检查CH423S硬件连接
   - 确认I2C通信正常
   - 查看CH423S初始化日志

3. **查询返回错误值**
   - 重新设置亮度级别
   - 检查系统初始化状态

### 调试命令
```json
{"cmd":"brightness","query":"1"}    // 查询当前状态
{"cmd":"brightness","test":"1"}     // 完整功能测试
```
