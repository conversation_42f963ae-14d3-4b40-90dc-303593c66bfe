﻿#include "logF.h"
#include "taskF.h"
#include "RTTStream.h"
#include "Arduino.h"
#include "hubF.h"
#include <cstdarg>

RTTStream rtt;

static bool g_initFlag = false;
static uint8_t g_logLevel = LOG_DEFAULT_LEVEL;

static const char* LOG_LEVEL_NAMES[] = {
    "OFF", "ERROR", "WARN", "INFO", "DEBUG"
};

static const char* LOG_LEVEL_COLORS[] = {
    "", "\033[31m", "\033[33m", "\033[32m", "\033[36m"
};

static const char* COLOR_RESET = "\033[0m";

const logInfoStru logS = {
    .initFlag = g_initFlag,
    .logLevel = g_logLevel
};

void logPrint(uint8_t level, const char *tag, const char *format, ...)
{
    if (level == LOG_OFF || level > g_logLevel) {
        return;
    }
    if (!tag || !format) {
        return;
    }
    uint32_t timestamp = millis();

    // 只使用RTT输出，移除Serial输出避免阻塞
    rtt.printf("[%lu] %s%s%s [%s] ", timestamp, LOG_LEVEL_COLORS[level], LOG_LEVEL_NAMES[level], COLOR_RESET, tag);
    va_list args;
    va_start(args, format);
    char buffer[256];
    vsnprintf(buffer, sizeof(buffer), format, args);
    rtt.print(buffer);
    va_end(args);
    rtt.println();
}

void logSetLevel(uint8_t level)
{
    if (level <= LOG_DEBUG) {
        g_logLevel = level;
        LOG_I("LOG", "日志级别设置为: %s", LOG_LEVEL_NAMES[level]);
    }
}

// ==================== 指令转发功能 ====================

/**
 * @brief 处理接收到的指令
 * @param command 完整的指令字符串
 */
static void processCommand(const char* command) {
    LOG_D("INPUT", "转发指令到HUB: %s", command);

    // 转发给指令处理中心
    hubProcessCommand(command);
}



void logInit()
{
    g_initFlag = true;

    // 启用LogProcess任务处理指令 - 提高频率到1ms
    taskRegister("LogProcess", 1, logProcessInput, true);

    LOG_I("LOG", "日志模块初始化完成");
    LOG_I("LOG", "当前日志级别: %s", LOG_LEVEL_NAMES[g_logLevel]);
    LOG_I("LOG", "指令接收模块已启动，指令将转发给HUB处理");
}

/**
 * @brief 处理RTT输入
 */
void logProcessInput()
{
    // 基于超时的RTT输入处理
    static String inputBuffer = "";
    static uint32_t lastInputTime = 0;
    static bool hasInput = false;
    const uint32_t INPUT_TIMEOUT = 50;  // 50ms超时，极速响应

    // 检查是否有新字符
    if (rtt.available()) {
        char c = rtt.read();

        if (c == '\n' || c == '\r') {
            // 收到换行符，清空缓冲区但不处理（因为JSON指令已经在}时处理了）
            if (inputBuffer.length() > 0) {
                LOG_D("INPUT", "清空缓冲区: %s", inputBuffer.c_str());
                inputBuffer = "";
                hasInput = false;
            }
        } else if (c >= 32 || (uint8_t)c >= 128) {
            // 可打印字符或UTF-8字符，添加到缓冲区
            inputBuffer += c;
            lastInputTime = millis();  // 更新最后输入时间
            hasInput = true;

            // 检查是否收到完整的JSON指令
            if (c == '}' && inputBuffer.indexOf('{') >= 0) {
                // 收到完整JSON，立即处理
                LOG_D("INPUT", "收到完整JSON指令: %s", inputBuffer.c_str());
                processCommand(inputBuffer.c_str());
                inputBuffer = "";
                hasInput = false;
                return;  // 立即返回，不继续处理
            }

            // 防止缓冲区过长（基于RTT配置的BUFFER_SIZE_DOWN）
            if (inputBuffer.length() > 500) {  // 接近RTT的512字节限制
                LOG_W("INPUT", "指令过长，已丢弃: %s", inputBuffer.c_str());
                inputBuffer = "";
                hasInput = false;
            }
        }
        return;  // 处理完一个字符就返回，真正的非阻塞
    }

    // 检查超时
    if (hasInput && (millis() - lastInputTime >= INPUT_TIMEOUT)) {
        LOG_D("INPUT", "指令超时处理: %s", inputBuffer.c_str());

        // 处理超时的指令
        if (inputBuffer.indexOf('{') >= 0 && inputBuffer.indexOf('}') >= 0) {
            processCommand(inputBuffer.c_str());
        } else {
            LOG_W("INPUT", "超时指令格式错误: %s", inputBuffer.c_str());
        }

        inputBuffer = "";
        hasInput = false;
    }
}
