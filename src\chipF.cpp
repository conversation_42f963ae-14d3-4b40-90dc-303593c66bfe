/**
 * @file chipF.cpp
 * @brief 芯片功能模块实现文件
 * @details 实现芯片相关功能，包括内存监控、芯片信息查询等
 */

#include "chipF.h"
#include "taskF.h"
#include "logF.h"
#include "ch423sF.h"
#include "Arduino.h"
#include "HardwareTimer.h"

// 内部状态变量
static bool g_chipInitialized = false;
static uint32_t g_freeMemory = 0;
static const uint32_t g_totalMemory = 20480;  // STM32F103C8 总RAM (20KB)
static uint8_t g_memoryUsage = 0;

// 全局状态结构体
const ChipInfo chipInfo = {
    .initialized = g_chipInitialized,
    .freeMemory = g_freeMemory,
    .totalMemory = g_totalMemory,
    .memoryUsage = g_memoryUsage
};

/**
 * @brief 初始化芯片模块
 */
void chipInit()
{
    // 注册内存监控任务到调度器
    taskRegister("MemCheck", 5000, taskMemoryCheck, true);

    // 初始化内存状态
    chipGetFreeMemory();  // 更新初始内存状态

    // CH423S使用同步模式，不需要定时器

    // 标记初始化完成
    g_chipInitialized = true;

    LOG_I("CHIP", "芯片模块初始化完成");
    LOG_I("CHIP", "总内存: %lu 字节 (%lu KB)", g_totalMemory, g_totalMemory / 1024);
    LOG_I("CHIP", "已注册内存监控任务: MemCheck");
}

/**
 * @brief 获取剩余内存
 */
uint32_t chipGetFreeMemory()
{
    char stackTop;
    extern char _end;
    uint32_t freeMemory = &stackTop - &_end;
    g_freeMemory = freeMemory;  // 更新全局状态

    // 计算内存使用率（整数百分比）
    g_memoryUsage = (uint8_t)(((g_totalMemory - freeMemory) * 100) / g_totalMemory);

    return freeMemory;
}

/**
 * @brief 获取总内存大小
 */
uint32_t chipGetTotalMemory()
{
    return g_totalMemory;
}

/**
 * @brief 获取内存使用率
 * @return 内存使用率(百分比，0-100)
 */
uint8_t chipGetMemoryUsage()
{
    return g_memoryUsage;
}

/**
 * @brief 内存检查任务
 */
void taskMemoryCheck()
{
    uint32_t freeMemory = chipGetFreeMemory();

    // 内存警告检查
    if (freeMemory < 1024) {
        LOG_W("MEM", "内存不足警告: %lu 字节", freeMemory);
    } else if (freeMemory < 2048) {
        LOG_W("MEM", "内存偏低提醒: %lu 字节", freeMemory);
    }

    // 输出内存状态（合并为一行）
    uint32_t usedMemory = g_totalMemory - freeMemory;
    uint8_t usagePercent = chipGetMemoryUsage();  // 使用新的函数
    LOG_D("MEM", "内存状态: 剩余 %lu 字节, 使用率 %u%%, 已用 %lu 字节",
          freeMemory, usagePercent, usedMemory);
}

// 定时器相关代码已移除，CH423S使用同步模式
