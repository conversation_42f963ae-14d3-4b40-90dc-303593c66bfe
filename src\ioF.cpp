/**
 * @file ioF.cpp
 * @brief IO功能模块实现文件
 * @details 实现GPIO控制、LED管理等IO相关功能
 */

#include "ioF.h"
#include "taskF.h"
#include "logF.h"
#include "Arduino.h"

// 内部状态变量
static bool g_ioInitialized = false;
static bool g_ledState = false;

// 全局状态结构体
const IoInfo ioInfo = {
    .initialized = g_ioInitialized,
    .ledState = g_ledState
};

/**
 * @brief 初始化IO模块
 */
void ioInit()
{
    // 配置PC13为输出模式 (LED)
    pinMode(PC13, OUTPUT);

    // 注册LED相关任务到调度器
    taskRegister("LedBlink", 1000, taskLedBlink, true);

    // 标记初始化完成
    g_ioInitialized = true;

    LOG_I("IO", "IO模块初始化完成");
    LOG_I("IO", "PC13 LED已配置为输出模式");
    LOG_I("IO", "已注册LED任务: LedBlink");
}

/**
 * @brief 设置LED状态
 */
void ioLedSet(bool state)
{
    g_ledState = state;
    digitalWrite(PC13, g_ledState);
    LOG_D("LED", "%s", g_ledState ? "ON" : "OFF");
}

/**
 * @brief 切换LED状态
 */
void ioLedToggle()
{
    g_ledState = !g_ledState;
    digitalWrite(PC13, g_ledState);
    LOG_D("LED", "%s", g_ledState ? "ON" : "OFF");
}

/**
 * @brief 获取LED当前状态
 */
bool ioLedGetState()
{
    return g_ledState;
}

/**
 * @brief LED闪烁任务
 */
void taskLedBlink()
{
    ioLedToggle();
}