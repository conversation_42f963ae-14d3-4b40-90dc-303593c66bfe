/**
 * @file ch423sF.h
 * @brief CH423S 64位按钮LED控制模块
 */

#pragma once

#include <stdint.h>
#include <stdbool.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// ==================== 64位按钮状态控制API ====================

/**
 * @brief 设置指定按钮的LED状态 (行列方式)
 * @param row 行号 (0-7)
 * @param col 列号 (0-7)
 * @param state LED状态: 0=关闭, 1=红色, 2=绿色, 3=红绿双色
 */
void ch423sSetButtonLed(uint8_t row, uint8_t col, uint8_t state);

/**
 * @brief 设置指定按钮的LED状态 (直接索引方式)
 * @param button_index 按钮索引 (0-63)
 * @param state LED状态: 0=关闭, 1=红色, 2=绿色, 3=红绿双色
 */
void ch423sSetButtonState(uint8_t button_index, uint8_t state);

/**
 * @brief 获取指定按钮的LED状态
 * @param button_index 按钮索引 (0-63)
 * @return LED状态: 0=关闭, 1=红色, 2=绿色, 3=红绿双色
 */
uint8_t ch423sGetButtonState(uint8_t button_index);

/**
 * @brief 批量设置按钮状态
 * @param states 64个按钮的状态数组
 */
void ch423sSetAllButtonStates(const uint8_t* states);

/**
 * @brief 获取所有按钮状态
 * @param states 输出64个按钮的状态数组
 */
void ch423sGetAllButtonStates(uint8_t* states);

/**
 * @brief 清除所有LED
 */
void ch423sClearAllLeds(void);

/**
 * @brief CH423S模块任务 - 高速循环更新显示
 * @note 需要定期调用，建议100ms间隔
 */
void ch423sModuleTask(void);

// ==================== K65按钮控制API ====================

/**
 * @brief 设置K65按钮LED状态
 * @param state LED状态: 0=关闭, 1=红色, 2=绿色, 3=红绿双色
 */
void ch423sSetK65ButtonState(uint8_t state);

/**
 * @brief 获取K65按钮LED状态
 * @return LED状态: 0=关闭, 1=红色, 2=绿色, 3=红绿双色
 */
uint8_t ch423sGetK65ButtonState(void);

// ==================== 亮度控制API ====================

/**
 * @brief 亮度级别枚举
 */
typedef enum {
    CH423S_BRIGHTNESS_LEVEL_LOW    = 0,    // 低亮度
    CH423S_BRIGHTNESS_LEVEL_MID    = 1,    // 中等亮度
    CH423S_BRIGHTNESS_LEVEL_HIGH   = 2,    // 高亮度
    CH423S_BRIGHTNESS_LEVEL_MAX    = 3     // 最高亮度
} ch423s_brightness_level_t;

/**
 * @brief 设置CH423S LED亮度级别
 * @param level 亮度级别 (0=低, 1=中, 2=高, 3=最高)
 * @return true 设置成功, false 设置失败
 */
bool ch423sSetBrightness(ch423s_brightness_level_t level);

/**
 * @brief 获取当前亮度级别
 * @return 当前亮度级别
 */
ch423s_brightness_level_t ch423sGetBrightness(void);

/**
 * @brief 测试所有亮度级别 (用于调试)
 * @note 会依次测试所有亮度级别，每级别显示3秒，最后恢复到最高亮度
 */
void ch423sTestBrightness(void);

#ifdef __cplusplus
}
#endif
