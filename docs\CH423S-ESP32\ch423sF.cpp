#ifndef CH423SF_H
#define CH423SF_H

// 引入头
#include "headerF.cpp"

/* 2线接口的连接,与实际电路有关 */

unsigned int CH423_SCL = 17;

unsigned int CH423_SDA = 16;

/* 2线接口的位操作,与单片机有关 */

#define CH423_SCL_SET                  \
    {                                  \
        digitalWrite(CH423_SCL, HIGH); \
    }

#define CH423_SCL_CLR                 \
    {                                 \
        digitalWrite(CH423_SCL, LOW); \
    }

#define CH423_SCL_D_OUT             \
    {                               \
        pinMode(CH423_SCL, OUTPUT); \
    } // 设置SCL为输出方向,对于双向I/O需切换为输出

#define CH423_SDA_SET                  \
    {                                  \
        digitalWrite(CH423_SDA, HIGH); \
    }

#define CH423_SDA_CLR                 \
    {                                 \
        digitalWrite(CH423_SDA, LOW); \
    }

// #define CH423_SDA_IN        { digitalRead(CH423_SDA) }

#define CH423_SDA_D_OUT             \
    {                               \
        pinMode(CH423_SDA, OUTPUT); \
    } // 设置SDA为输出方向,对于双向I/O需切换为输出

#define CH423_SDA_D_IN             \
    {                              \
        pinMode(CH423_SDA, INPUT); \
    } // 设置SDA为输入方向,对于双向I/O需切换为输入

// CH423接口定义
#define CH423_I2C_ADDR1 0x40 // CH423的地址
#define CH423_I2C_MASK 0x3E  // CH423的高字节命令掩码

/*  设置系统参数命令 */
#define CH423_SYS_CMD 0x4800 // 设置系统参数命令，默认方式
// #define BIT_SLEEP 0x30       // 低功耗睡眠控制
// #define BIT_INTENS 0x20      // 动态显示驱动亮度控制
// #define BIT_OD_EN 0x10       // 输出引脚 OC15～OC0 开漏输出使能
#define BIT_X_INT 0x08 // 使能输入电平变化中断，为0禁止输入电平变化中断；为1并且DEC_H为0允许输出电平变化中断
#define BIT_DEC_H 0x04 // 控制开漏输出引脚高8位的片选译码
#define BIT_DEC_L 0x02 // 控制开漏输出引脚低8位的片选译码
#define BIT_IO_OE 0x01 // 控制双向输入输出引脚的三态输出，为1允许输出

/*  设置低8位开漏输出命令 */
#define CH423_OC_L_CMD 0x4400 // 设置低8位开漏输出命令，默认方式
#define BIT_OC0_L_DAT 0x01    // OC0为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC1_L_DAT 0x02    // OC1为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC2_L_DAT 0x04    // OC2为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC3_L_DAT 0x08    // OC3为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC4_L_DAT 0x10    // OC4为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC5_L_DAT 0x20    // OC5为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC6_L_DAT 0x40    // OC6为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC7_L_DAT 0x80    // OC7为0则使引脚输出低电平，为1则引脚不输出

/*  设置高8位开漏输出命令 */
#define CH423_OC_H_CMD 0x4600 // 设置低8位开漏输出命令，默认方式
#define BIT_OC8_L_DAT 0x01    // OC8为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC9_L_DAT 0x02    // OC9为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC10_L_DAT 0x04   // OC10为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC11_L_DAT 0x08   // OC11为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC12_L_DAT 0x10   // OC12为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC13_L_DAT 0x20   // OC13为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC14_L_DAT 0x40   // OC14为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC15_L_DAT 0x80   // OC15为0则使引脚输出低电平，为1则引脚不输出

/*  设置高8位开漏输出命令 */
#define CH423_H_CMD 0x0600    // 设置低8位推挽输出命令，默认方式
#define CH423_OC_H_CMD 0x4600 // 设置低8位开漏输出命令，默认方式
#define BIT_OC8_L_DAT 0x01    // OC8为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC9_L_DAT 0x02    // OC9为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC10_L_DAT 0x04   // OC10为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC11_L_DAT 0x08   // OC11为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC12_L_DAT 0x10   // OC12为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC13_L_DAT 0x20   // OC13为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC14_L_DAT 0x40   // OC14为0则使引脚输出低电平，为1则引脚不输出
#define BIT_OC15_L_DAT 0x80   // OC15为0则使引脚输出低电平，为1则引脚不输出

/* 设置双向输入输出命令 */

#define CH423_SET_IO_CMD 0x6000 // 设置双向输入输出命令，默认方式
#define BIT_IO0_DAT 0x01        // 写入双向输入输出引脚的输出寄存器，当IO_OE=1,IO0为0输出低电平，为1输出高电平
#define BIT_IO1_DAT 0x02        // 写入双向输入输出引脚的输出寄存器，当IO_OE=1,IO1为0输出低电平，为1输出高电平
#define BIT_IO2_DAT 0x04        // 写入双向输入输出引脚的输出寄存器，当IO_OE=1,IO2为0输出低电平，为1输出高电平
#define BIT_IO3_DAT 0x08        // 写入双向输入输出引脚的输出寄存器，当IO_OE=1,IO3为0输出低电平，为1输出高电平
#define BIT_IO4_DAT 0x10        // 写入双向输入输出引脚的输出寄存器，当IO_OE=1,IO4为0输出低电平，为1输出高电平
#define BIT_IO5_DAT 0x20        // 写入双向输入输出引脚的输出寄存器，当IO_OE=1,IO5为0输出低电平，为1输出高电平
#define BIT_IO6_DAT 0x40        // 写入双向输入输出引脚的输出寄存器，当IO_OE=1,IO6为0输出低电平，为1输出高电平
#define BIT_IO7_DAT 0x80        // 写入双向输入输出引脚的输出寄存器，当IO_OE=1,IO7为0输出低电平，为1输出高电平

/* 读取双向输入输出命令 */
#define CH423_RD_IO_CMD 0x4D // 输入I/O引脚当前状态

// 对外子程序
extern void CH423_WriteByte(unsigned short cmd); // 写出数据
extern unsigned char CH423_ReadByte(void);       // 读取数据

// 特定用途子程序
extern void CH423_Write(unsigned short cmd); // 向CH423发出操作命令,该子程序与CH423_WriteByte不同，区别主要是前者先将命令码高8位移位
// 下述定义仅适用于CH423_Write子程序，这样定义是为了兼容I2C数据，如果不考虑兼容，那么高8位应该先左移1位
#define CH423_DIG0 0x1000  // 数码管位0显示
#define CH423_DIG1 0x1100  // 数码管位1显示
#define CH423_DIG2 0x1200  // 数码管位2显示
#define CH423_DIG3 0x1300  // 数码管位3显示
#define CH423_DIG4 0x1400  // 数码管位4显示
#define CH423_DIG5 0x1500  // 数码管位5显示
#define CH423_DIG6 0x1600  // 数码管位6显示
#define CH423_DIG7 0x1700  // 数码管位7显示
#define CH423_DIG8 0x1800  // 数码管位8显示
#define CH423_DIG9 0x1900  // 数码管位9显示
#define CH423_DIG10 0x1A00 // 数码管位10显示
#define CH423_DIG11 0x1B00 // 数码管位11显示
#define CH423_DIG12 0x1C00 // 数码管位12显示
#define CH423_DIG13 0x1D00 // 数码管位13显示
#define CH423_DIG14 0x1E00 // 数码管位14显示
#define CH423_DIG15 0x1F00 // 数码管位15显示

#define CH423_SYSON1 0x0417 // 开启自动扫描显示
// #define SPACE 0x10          // 空格

void CH423_I2c_Start(void) // 操作起始
{
    CH423_SDA_SET;   // 发送起始条件的数据信号
    CH423_SDA_D_OUT; // 设置SDA为输出方向
    CH423_SCL_SET;
    CH423_SCL_D_OUT; // 设置SCL为输出方向
    delayMicroseconds(1);
    CH423_SDA_CLR; // 发送起始信号
    delayMicroseconds(1);
    CH423_SCL_CLR; // 钳住I2C总线，准备发送或接收数据
}

void CH423_I2c_Stop(void) // 操作结束
{
    CH423_SDA_CLR;
    delayMicroseconds(1);
    CH423_SCL_SET;
    delayMicroseconds(1);
    CH423_SDA_SET; // 发送I2C总线结束信号
    delayMicroseconds(1);
}

void CH423_I2c_WrByte(unsigned char dat) // 写一个字节数据
{
    unsigned char i;
    for (i = 0; i != 8; i++) // 输出8位数据
    {
        if (dat & 0x80)
        {
            CH423_SDA_SET;
        }
        else
        {
            CH423_SDA_CLR;
        }
        delayMicroseconds(1);
        CH423_SCL_SET;
        dat <<= 1;
        delayMicroseconds(1);
        ; // 可选延时

        CH423_SCL_CLR;
    }
    CH423_SDA_SET;
    delayMicroseconds(1);
    CH423_SCL_SET; // 接收应答
    delayMicroseconds(1);
    CH423_SCL_CLR;
}

unsigned char CH423_I2c_RdByte(void) // 读一个字节数据
{
    unsigned char dat, i;
    CH423_SDA_SET;
    CH423_SDA_D_IN; // 设置SDA为输入方向
    dat = 0;
    for (i = 0; i != 8; i++) // 输入8位数据
    {
        delayMicroseconds(1);
        ; // 可选延时
        CH423_SCL_SET;
        delayMicroseconds(1);
        ; // 可选延时
        dat <<= 1;
        if (digitalRead(CH423_SDA) == HIGH)
        {
            dat++; // 输入1位
        }

        CH423_SCL_CLR;
    }

    CH423_SDA_SET;
    delayMicroseconds(1);
    CH423_SCL_SET; // 发出无效应答
    delayMicroseconds(1);
    CH423_SCL_CLR;
    return (dat);
}

void CH423_Write(unsigned short cmd) // 写命令
{
    CH423_I2c_Start(); // 启动总线
    CH423_I2c_WrByte(((unsigned char)(cmd >> 7) & CH423_I2C_MASK) | CH423_I2C_ADDR1);
    CH423_I2c_WrByte((unsigned char)cmd); // 发送数据
    CH423_I2c_Stop();                     // 结束总线
}

void CH423_WriteByte(unsigned short cmd) // 写出数据
{
    CH423_I2c_Start(); // 启动总线
    CH423_I2c_WrByte((unsigned char)(cmd >> 8));
    CH423_I2c_WrByte((unsigned char)cmd); // 发送数据
    CH423_I2c_Stop();                     // 结束总线
}

unsigned char CH423_ReadByte() // 读取数据
{
    unsigned char din;
    CH423_I2c_Start();                 // 启动总线
    CH423_I2c_WrByte(CH423_RD_IO_CMD); // 此值为0x4D
    din = CH423_I2c_RdByte();          // 读取数据
    CH423_I2c_Stop();                  // 结束总线
    return (din);
}

void CH423_buf_write(unsigned short cmd) // 向CH423输出数据或者操作命令，自动建立数据映象
{
    if (cmd & 0x1000)
    {                                                                                         // 加载数据的命令,需要备份数据到映象缓冲区
        ch423s.numberDisplay[(unsigned char)(cmd >> 8) & 0x0F] = (unsigned char)(cmd & 0xFF); // 备份数据到相应的映象单元
    }
    CH423_Write(cmd); // 发出
}

void CH423_buf_index(unsigned char index, unsigned char dat) // 向CH423指定的数码管输出数据,自动建立数据映象
{                                                            // index 为数码管序号,有效值为0到15,分别对应DIG0到DIG15
    unsigned short cmd;
    ch423s.numberDisplay[index] = dat;                       // 备份数据到相应的映象单元
    cmd = (CH423_DIG0 + ((unsigned short)index << 8)) | dat; // 生成操作命令
    CH423_Write(cmd);                                        // 发出
}

void CH423_set_bit(unsigned char bit_addr) // 段位点亮
{
    unsigned char byte_addr;
    byte_addr = (bit_addr >> 3) & 0x0F;
    CH423_buf_index(byte_addr, ch423s.numberDisplay[byte_addr] | (1 << (bit_addr & 0x07)));
}

void CH423_clr_bit(unsigned char bit_addr) // 段位熄灭
{
    unsigned char byte_addr;
    byte_addr = (bit_addr >> 3) & 0x0F;
    CH423_buf_index(byte_addr, ch423s.numberDisplay[byte_addr] & ~(1 << (bit_addr & 0x07)));
}

void LEFTMOV() // 左移
{
    unsigned short i;
    for (i = 0x000F; i >= 0x0001; i--)
    {
        CH423_buf_index(i, ch423s.numberDisplay[i - 1]);
    }
    CH423_Write(0x1000);
}

void LEFTCYC() // 左循
{
    unsigned short i, j;
    j = ch423s.numberDisplay[15];
    for (i = 0x000F; i >= 0x0001; i--)
    {
        CH423_buf_index(i, ch423s.numberDisplay[i - 1]);
    }
    CH423_Write(0x1000 | j);
}

void RIGHTMOV() // 右移
{
    unsigned short i;
    for (i = 0x0000; i < 0x000F; i++)
    {
        CH423_buf_index(i, ch423s.numberDisplay[i + 1]);
    }
    CH423_Write(0x1F00);
}

void RIGHTCYC() // 右循
{
    unsigned char i, j;
    j = ch423s.number[0];
    for (i = 0x0000; i < 0x000F; i++)
    {
        CH423_buf_index(i, ch423s.numberDisplay[i + 1]);
    }
    CH423_Write(0x1F00 | j);
}

void TWINKLE(unsigned char dig_number) // 闪烁
{
    unsigned char old;
    old = ch423s.numberDisplay[dig_number & 0x0F];
    CH423_buf_index(dig_number, 0x00);
    delay(250);
    CH423_buf_index(dig_number, old);
    delay(250);
}

// 刷新寄存器
void CH423sRefresh()
{
    // 改变4-8之间的寄存器数据(全部数据为0-15)
    for (int i = 4; i < 8; i++)
    {
        // 处理缓存数据
        ch423s.numberDisplay[i] = ch423s.BCD_decode_tab[ch423s.number[i]];
        // 更新寄存器
        CH423_buf_index(i, ch423s.numberDisplay[i]);
    }
}

// 进程
void ch423sProcess()
{
    // 判断初始化
    if (ch423s.init != 1)
    {
        return;
    }

    // 对按钮的状态转换显示周期
    if (millis() - ch423s.buttonFlashTime >= 1000)
    {
        ch423s.buttonFlashTime = millis();

        // 如果电池电量低
        if (bat.state == -1)
        {

            if (ch423s.buttonFlash == 0)
            {
                // 更新寄存器
                for (size_t i = 9; i < 16; i++)
                {
                    CH423_buf_index(i, 0xFF);
                }
                printf("显示位:%d;\r\n", ch423s.buttonFlash);
                ch423s.buttonFlash = 1;
            }
            else
            {
                // 更新寄存器
                for (size_t i = 9; i < 16; i++)
                {
                    CH423_buf_index(i, 0x00);
                }
                printf("显示位:%d;\r\n", ch423s.buttonFlash);
                ch423s.buttonFlash = 0;
            }
        }
    }
}

// 线程实例化(微秒)
Ticker ch423sTask(ch423sProcess, 50, 0, MILLIS);

// 初始化
void ch423sInit()
{
    printf("%s\r\n", "+++++++++++++++++++++++++++++++++++++CH423S正在初始化...");

    // 设置系统参数命令
    CH423_WriteByte(CH423_SYS_CMD | BIT_DEC_H | BIT_DEC_L | BIT_IO_OE);

    // 因为CH423复位时不清空显示内容，所以刚开电后必须人为清空，再开显示
    for (int i = 0; i < 16; i++)
    {
        CH423_buf_index(i, 0);
    }

    // 刷新数据
    CH423sRefresh();

    // 开启显示(开启自动扫描后,8号端口不受控,具体问题后续测试)
    // CH423_buf_write(CH423_SYSON1);

    // 启动线程调度
    ch423sTask.start();

    // 标记初始化完成
    ch423s.init = 1;

    printf("%s\r\n\r\n", "+++++++++++++++++++++++++++++++++++++CH423S初始化进度完成!");
}

// 调度器
void ch423sDispatch()
{
    ch423sTask.update();
}

#endif // CH423SF_H