# 🚀 CH423S系统快速使用指南

## 📋 **5分钟快速上手**

本指南帮助你快速开始使用CH423S LED控制系统，无需深入了解技术细节即可开始控制LED。

## 🎯 **系统简介**

CH423S系统是一个高性能的LED矩阵控制系统，支持：
- **64个按钮LED**: K01-K64，每个支持红/绿/双色
- **1个独立按钮**: K65，支持红/绿/双色
- **JSON指令控制**: 简单易用的指令格式
- **实时响应**: ~10ms级别的快速响应

## 🔌 **硬件连接**

### 必需连接
```
STM32    ←→    CH423S
PA6      ←→    SCL (时钟线)
PA7      ←→    SDA (数据线)
3.3V     ←→    VCC (电源)
GND      ←→    GND (地线)
```

### 上拉电阻
- SCL线需要4.7kΩ上拉电阻到VCC
- SDA线需要4.7kΩ上拉电阻到VCC

### K65独立按钮 (可选)
```
STM32    ←→    LED
PB1      ←→    K65红色LED
PB11     ←→    K65绿色LED
```

## 💻 **软件准备**

### 1. 烧录固件
- 编译项目并烧录到STM32
- 确保编译成功无错误

### 2. 连接调试工具
- 使用J-Link RTT Viewer连接
- 或使用串口调试工具
- 波特率：115200 (如使用串口)

### 3. 验证连接
看到类似日志表示系统正常启动：
```
[INFO] 模块化系统启动
[INFO] CH423S检测: PA6(SCL), PA7(SDA)
[INFO] CH423S自动扫描模式初始化完成
```

## 🎮 **基本指令格式**

### JSON指令结构
```json
{"cmd":"指令类型","按钮":"状态"}
```

### LED状态值
- `"0"`: 关闭LED
- `"1"`: 红色LED
- `"2"`: 绿色LED  
- `"3"`: 红绿双色LED

## 🧪 **第一次测试**

### 步骤1: 测试单个LED
在RTT Viewer或串口工具中输入：
```json
{"cmd":"led","k01":"1"}
```
**预期结果**: K01按钮的红色LED点亮

### 步骤2: 测试绿色LED
```json
{"cmd":"led","k01":"2"}
```
**预期结果**: K01按钮的绿色LED点亮

### 步骤3: 测试双色LED
```json
{"cmd":"led","k01":"3"}
```
**预期结果**: K01按钮的红绿LED同时点亮

### 步骤4: 关闭LED
```json
{"cmd":"led","k01":"0"}
```
**预期结果**: K01按钮的LED关闭

## 🎯 **常用指令示例**

### 基本LED控制
```json
{"cmd":"led","k01":"1"}    // 点亮K01红色
{"cmd":"led","k02":"2"}    // 点亮K02绿色
{"cmd":"led","k03":"3"}    // 点亮K03双色
{"cmd":"led","k04":"0"}    // 关闭K04
```

### 不同位置的按钮
```json
{"cmd":"led","k01":"1"}    // 第1个按钮 (左上角)
{"cmd":"led","k08":"1"}    // 第8个按钮 (右上角)
{"cmd":"led","k57":"1"}    // 第57个按钮 (左下角)
{"cmd":"led","k64":"1"}    // 第64个按钮 (右下角)
```

### K65独立按钮
```json
{"cmd":"led","k65":"1"}    // K65红色LED
{"cmd":"led","k65":"2"}    // K65绿色LED
{"cmd":"led","k65":"3"}    // K65双色LED
{"cmd":"led","k65":"0"}    // 关闭K65
```

### 快速切换效果
```json
{"cmd":"led","k01":"1"}
{"cmd":"led","k01":"0"}
{"cmd":"led","k02":"1"}
{"cmd":"led","k02":"0"}
{"cmd":"led","k03":"1"}
```

## 🗺️ **按钮位置图**

### 8×8矩阵布局
```
    列0  列1  列2  列3  列4  列5  列6  列7
行0  K01  K02  K03  K04  K05  K06  K07  K08
行1  K09  K10  K11  K12  K13  K14  K15  K16
行2  K17  K18  K19  K20  K21  K22  K23  K24
行3  K25  K26  K27  K28  K29  K30  K31  K32
行4  K33  K34  K35  K36  K37  K38  K39  K40
行5  K41  K42  K43  K44  K45  K46  K47  K48
行6  K49  K50  K51  K52  K53  K54  K55  K56
行7  K57  K58  K59  K60  K61  K62  K63  K64
```

### 位置计算
- **第1行**: K01-K08
- **第2行**: K09-K16
- **第3行**: K17-K24
- **第4行**: K25-K32
- **第5行**: K33-K40
- **第6行**: K41-K48
- **第7行**: K49-K56
- **第8行**: K57-K64

## 🎨 **创意使用示例**

### 流水灯效果
```json
{"cmd":"led","k01":"1"}
{"cmd":"led","k01":"0"}
{"cmd":"led","k02":"1"}
{"cmd":"led","k02":"0"}
{"cmd":"led","k03":"1"}
{"cmd":"led","k03":"0"}
```

### 边框效果
```json
// 点亮第一行
{"cmd":"led","k01":"1"}
{"cmd":"led","k02":"1"}
{"cmd":"led","k03":"1"}
{"cmd":"led","k04":"1"}
{"cmd":"led","k05":"1"}
{"cmd":"led","k06":"1"}
{"cmd":"led","k07":"1"}
{"cmd":"led","k08":"1"}
```

### 对角线效果
```json
{"cmd":"led","k01":"1"}    // 左上
{"cmd":"led","k10":"1"}    // 
{"cmd":"led","k19":"1"}    // 
{"cmd":"led","k28":"1"}    // 
{"cmd":"led","k37":"1"}    // 
{"cmd":"led","k46":"1"}    // 
{"cmd":"led","k55":"1"}    // 
{"cmd":"led","k64":"1"}    // 右下
```

### 彩色模式
```json
{"cmd":"led","k01":"1"}    // 红色
{"cmd":"led","k02":"2"}    // 绿色
{"cmd":"led","k03":"3"}    // 双色
{"cmd":"led","k04":"1"}    // 红色
{"cmd":"led","k05":"2"}    // 绿色
```

## ⚠️ **注意事项**

### 指令格式要求
- 必须是完整的JSON格式
- 使用双引号，不能用单引号
- 按钮编号必须是字符串 (`"k01"` 不是 `k01`)
- 状态值必须是字符串 (`"1"` 不是 `1`)

### 正确格式 ✅
```json
{"cmd":"led","k01":"1"}
```

### 错误格式 ❌
```json
{cmd:"led",k01:1}           // 缺少双引号
{"cmd":"led","k01":1}       // 状态值不是字符串
{'cmd':'led','k01':'1'}     // 使用单引号
```

### 按钮编号范围
- **矩阵按钮**: k01-k64 (不是k00或k65)
- **独立按钮**: k65
- **无效编号**: k00, k66, k99等

## 🔍 **故障排除**

### 问题1: LED不亮
**检查项目**:
- [ ] 硬件连接是否正确
- [ ] 指令格式是否正确
- [ ] 按钮编号是否在有效范围
- [ ] 系统是否正常启动

### 问题2: 指令无响应
**检查项目**:
- [ ] JSON格式是否正确
- [ ] 是否按回车键发送
- [ ] RTT连接是否正常
- [ ] 系统日志是否有错误

### 问题3: LED位置错乱
**解决方案**:
- 参考按钮位置图确认编号
- 检查硬件连接是否正确
- 查看系统日志确认映射

### 问题4: 响应慢
**检查项目**:
- [ ] 系统性能是否正常
- [ ] 是否有大量日志输出
- [ ] 硬件I2C通信是否稳定

## 📞 **获取帮助**

### 查看系统状态
系统启动后会显示详细的初始化信息，包括：
- 模块初始化状态
- CH423S检测结果
- 任务注册情况
- 错误信息 (如有)

### 调试信息
发送指令后会显示处理过程：
```
[DEBUG] 转发指令到HUB: {"cmd":"led","k01":"1"}
[INFO] 设置按钮k01(索引0,行0,列0) LED状态: 1
```

### 常见日志信息
- `[INFO] CH423S自动扫描模式初始化完成` - 系统正常
- `[ERROR] 开启自动扫描失败` - 硬件连接问题
- `[WARN] 未知指令类型` - 指令格式错误

## 🎉 **开始使用**

现在你已经掌握了基本使用方法，可以开始：

1. **测试基本功能**: 使用上面的示例指令
2. **创建自己的效果**: 组合不同的LED控制
3. **探索高级功能**: 查看其他技术文档
4. **开发应用**: 集成到你的项目中

祝你使用愉快！如果遇到问题，请参考故障排除部分或查看详细的技术文档。🚀
