2025-07-31键盘顺序整理

原始键盘顺序:
04,05,06,01,02,03,07,08,09,10,11,12,13
14,15,16,17,18,19,20,24,25,26,21,22,23
27,28,29,30,31,32,34,33,36,35,37,39,38
40,41,42,43,44,45,46,47,48,49,50,51,52
53,54,55,56,57,58,59,60,61,62,63,64,65

按键映射表 (按键编号 -> 对应的K编号):
按键1  -> K04    按键2  -> K05    按键3  -> K06    按键4  -> K01    按键5  -> K02
按键6  -> K03    按键7  -> K07    按键8  -> K08    按键9  -> K09    按键10 -> K10
按键11 -> K11    按键12 -> K12    按键13 -> K13    按键14 -> K14    按键15 -> K15
按键16 -> K16    按键17 -> K17    按键18 -> K18    按键19 -> K19    按键20 -> K20
按键21 -> K24    按键22 -> K25    按键23 -> K26    按键24 -> K21    按键25 -> K22
按键26 -> K23    按键27 -> K27    按键28 -> K28    按键29 -> K29    按键30 -> K30
按键31 -> K31    按键32 -> K32    按键33 -> K34    按键34 -> K33    按键35 -> K36
按键36 -> K35    按键37 -> K37    按键38 -> K39    按键39 -> K38    按键40 -> K40
按键41 -> K41    按键42 -> K42    按键43 -> K43    按键44 -> K44    按键45 -> K45
按键46 -> K46    按键47 -> K47    按键48 -> K48    按键49 -> K49    按键50 -> K50
按键51 -> K51    按键52 -> K52    按键53 -> K53    按键54 -> K54    按键55 -> K55
按键56 -> K56    按键57 -> K57    按键58 -> K58    按键59 -> K59    按键60 -> K60
按键61 -> K61    按键62 -> K62    按键63 -> K63    按键64 -> K64    按键65 -> K65

8x8矩阵布局 (按实际键盘顺序排列):
行1: K04  K05  K06  K01  K02  K03  K07  K08
行2: K09  K10  K11  K12  K13  K14  K15  K16
行3: K17  K18  K19  K20  K24  K25  K26  K21
行4: K22  K23  K27  K28  K29  K30  K31  K32
行5: K34  K33  K36  K35  K37  K39  K38  K40
行6: K41  K42  K43  K44  K45  K46  K47  K48
行7: K49  K50  K51  K52  K53  K54  K55  K56
行8: K57  K58  K59  K60  K61  K62  K63  K64
独立: K65

C语言数组定义 (按键编号到K编号的映射):
const uint8_t key_to_k_mapping[66] = {
    0,   // 索引0不使用
    4,   // 按键1  -> K04
    5,   // 按键2  -> K05
    6,   // 按键3  -> K06
    1,   // 按键4  -> K01
    2,   // 按键5  -> K02
    3,   // 按键6  -> K03
    7,   // 按键7  -> K07
    8,   // 按键8  -> K08
    9,   // 按键9  -> K09
    10,  // 按键10 -> K10
    11,  // 按键11 -> K11
    12,  // 按键12 -> K12
    13,  // 按键13 -> K13
    14,  // 按键14 -> K14
    15,  // 按键15 -> K15
    16,  // 按键16 -> K16
    17,  // 按键17 -> K17
    18,  // 按键18 -> K18
    19,  // 按键19 -> K19
    20,  // 按键20 -> K20
    24,  // 按键21 -> K24
    25,  // 按键22 -> K25
    26,  // 按键23 -> K26
    21,  // 按键24 -> K21
    22,  // 按键25 -> K22
    23,  // 按键26 -> K23
    27,  // 按键27 -> K27
    28,  // 按键28 -> K28
    29,  // 按键29 -> K29
    30,  // 按键30 -> K30
    31,  // 按键31 -> K31
    32,  // 按键32 -> K32
    34,  // 按键33 -> K34
    33,  // 按键34 -> K33
    36,  // 按键35 -> K36
    35,  // 按键36 -> K35
    37,  // 按键37 -> K37
    39,  // 按键38 -> K39
    38,  // 按键39 -> K38
    40,  // 按键40 -> K40
    41,  // 按键41 -> K41
    42,  // 按键42 -> K42
    43,  // 按键43 -> K43
    44,  // 按键44 -> K44
    45,  // 按键45 -> K45
    46,  // 按键46 -> K46
    47,  // 按键47 -> K47
    48,  // 按键48 -> K48
    49,  // 按键49 -> K49
    50,  // 按键50 -> K50
    51,  // 按键51 -> K51
    52,  // 按键52 -> K52
    53,  // 按键53 -> K53
    54,  // 按键54 -> K54
    55,  // 按键55 -> K55
    56,  // 按键56 -> K56
    57,  // 按键57 -> K57
    58,  // 按键58 -> K58
    59,  // 按键59 -> K59
    60,  // 按键60 -> K60
    61,  // 按键61 -> K61
    62,  // 按键62 -> K62
    63,  // 按键63 -> K63
    64,  // 按键64 -> K64
    65   // 按键65 -> K65
};

反向映射 (K编号到按键编号):
K01->按键4   K02->按键5   K03->按键6   K04->按键1   K05->按键2   K06->按键3   K07->按键7   K08->按键8
K09->按键9   K10->按键10  K11->按键11  K12->按键12  K13->按键13  K14->按键14  K15->按键15  K16->按键16
K17->按键17  K18->按键18  K19->按键19  K20->按键20  K21->按键24  K22->按键25  K23->按键26  K24->按键21
K25->按键22  K26->按键23  K27->按键27  K28->按键28  K29->按键29  K30->按键30  K31->按键31  K32->按键32
K33->按键34  K34->按键33  K35->按键36  K36->按键35  K37->按键37  K38->按键39  K39->按键38  K40->按键40
K41->按键41  K42->按键42  K43->按键43  K44->按键44  K45->按键45  K46->按键46  K47->按键47  K48->按键48
K49->按键49  K50->按键50  K51->按键51  K52->按键52  K53->按键53  K54->按键54  K55->按键55  K56->按键56
K57->按键57  K58->按键58  K59->按键59  K60->按键60  K61->按键61  K62->按键62  K63->按键63  K64->按键64
K65->按键65

注意事项:
1. 键盘物理布局与标准K01-K64顺序不同
2. 需要使用映射表进行转换
3. 某些位置有交换 (如K21-K26区域, K33-K39区域)
4. K65是独立按钮，不在8x8矩阵中

生成时间: 2025-07-31
