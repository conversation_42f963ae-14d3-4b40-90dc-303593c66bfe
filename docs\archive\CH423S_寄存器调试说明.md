# CH423S寄存器控制调试说明

## 问题现象
LED矩阵只有部分被点亮，说明寄存器控制存在问题。

## 可能的原因分析

### 1. 显示寄存器基地址错误
CH423S的显示位寄存器基地址可能不是0x1000，需要测试其他可能的地址：
- 0x1000 (当前使用)
- 0x1400 (备用地址1)  
- 0x1800 (备用地址2)

### 2. 寄存器映射问题
8x8 LED矩阵的寄存器映射可能需要调整：
- DIG0-DIG7: 红色LED行扫描
- DIG8-DIG15: 绿色LED行扫描
- 或者其他映射方式

### 3. 命令格式问题
显示位命令的格式可能需要调整：
```cpp
// 当前格式
uint16_t cmd = CH423S_DIG_BASE + (位号 << 8) + 数据;

// 可能的其他格式
uint16_t cmd = CH423S_DIG_BASE + 位号 + (数据 << 8);
```

## 调试步骤

### 阶段0: 测试基本输出功能
测试CH423S的开漏输出是否正常工作：
```cpp
// 低8位输出测试
uint16_t low_cmd = 0x4400 + 0xFF;  // OC0-OC7全部输出高电平

// 高8位输出测试  
uint16_t high_cmd = 0x4600 + 0xFF;  // OC8-OC15全部输出高电平
```

### 阶段1: 测试显示寄存器基地址
依次测试不同的基地址，找到正确的寄存器地址：
```cpp
uint16_t base_addresses[] = {0x1000, 0x1400, 0x1800};
```

### 阶段2: 测试单个LED控制
确认单个LED的控制逻辑是否正确。

### 阶段3: 全面LED测试
测试所有LED的红色、绿色、双色显示。

## 调试输出说明

### 关键日志信息
1. **基本输出测试**:
   ```
   [INFO] 测试低8位输出 (OC0-OC7)
   [INFO] 低8位输出命令发送成功: 0x44FF
   ```

2. **显示寄存器测试**:
   ```
   [INFO] 测试基地址 0x1000
   [INFO] 发送测试命令: 0x10FF
   [INFO] 基地址 0x1000 测试成功
   ```

3. **LED控制详情**:
   ```
   [DEBUG] 行0: 红色=0x01, 绿色=0x00
   [DEBUG] 发送显示位0命令: 0x1001 (数据: 0x01)
   ```

## 硬件检查要点

### LED矩阵连接
确认LED矩阵的连接方式：
- 共阴极还是共阳极
- 行扫描还是列扫描
- 红绿LED的引脚分配

### CH423S引脚功能
- OC0-OC15: 开漏输出，用于LED驱动
- DIG0-DIG15: 显示位寄存器，用于自动扫描
- IO0-IO7: 双向I/O，可用于按键扫描

### 电路检查
- LED限流电阻是否合适
- 电源电压是否稳定
- 信号线是否有干扰

## 预期结果

### 成功的标志
1. 基本输出测试能看到LED响应
2. 找到正确的显示寄存器基地址
3. 单个LED能正确控制
4. 8x8矩阵能完整显示

### 失败的处理
1. 如果基本输出都失败，检查I2C通信
2. 如果显示寄存器都失败，可能需要使用开漏输出模式
3. 如果部分LED不亮，检查硬件连接

## 备用方案

如果自动扫描模式无法正常工作，可以考虑：
1. 使用开漏输出模式手动扫描
2. 调整LED矩阵的连接方式
3. 使用不同的驱动策略

## 下一步行动

1. 运行修改后的代码，观察调试输出
2. 根据日志信息确定问题所在
3. 如有需要，进一步调整寄存器映射或命令格式
4. 验证硬件连接的正确性
