#pragma once
#include <stdint.h>
#include <stdbool.h>

/**
 * @file ch423sF.h
 * @brief CH423S I/O扩展芯片同步驱动模块
 * @details 基于软件模拟I2C的同步CH423S驱动，便于硬件调试
 *
 * 硬件连接：
 * - PA6 -> CH423S SCL
 * - PA7 -> CH423S SDA
 * - 3.3V/5V -> CH423S VCC
 * - GND -> CH423S GND
 *
 * 使用示例：
 * 1. 初始化：ch423sInit();
 * 2. 设置输出：ch423sSetOutput(0x1234);
 * 3. 读取GPIO：uint8_t gpio = ch423sReadGpio();
 * 4. 检查通信：bool ok = ch423sTestCommunication();
 */

// CH423S硬件配置
#define CH423S_SCL_PIN    6     // PA6
#define CH423S_SDA_PIN    7     // PA7
#define CH423S_GPIO_PORT  GPIOA

// CH423S协议定义
#define CH423S_I2C_ADDR      0x40    // 基础I2C地址
#define CH423S_I2C_MASK      0x3E    // 地址掩码

// CH423S命令定义
#define CH423S_SYS_CMD       0x4800  // 系统参数设置命令
#define CH423S_OC_L_CMD      0x4400  // 低8位开漏输出命令
#define CH423S_OC_H_CMD      0x4600  // 高8位开漏输出命令
#define CH423S_SET_IO_CMD    0x6000  // 设置双向I/O命令
#define CH423S_RD_IO_CMD     0x4D    // 读取双向I/O命令

// 系统参数位定义
#define CH423S_BIT_X_INT     0x08    // 使能输入电平变化中断
#define CH423S_BIT_DEC_H     0x04    // 高8位片选译码控制
#define CH423S_BIT_DEC_L     0x02    // 低8位片选译码控制
#define CH423S_BIT_IO_OE     0x01    // 双向I/O输出使能

/**
 * @brief CH423S通信结果定义
 */
typedef enum {
    CH423S_OK = 0,          // 通信成功
    CH423S_ERROR_TIMEOUT,   // 超时错误
    CH423S_ERROR_NACK,      // 无应答错误
    CH423S_ERROR_BUS,       // 总线错误
    CH423S_ERROR_PARAM      // 参数错误
} CH423S_Result_t;

/**
 * @brief CH423S驱动状态结构体
 */
typedef struct {
    bool initialized;               // 初始化标志
    uint16_t output_cache;          // 输出状态缓存
    uint8_t gpio_cache;             // GPIO状态缓存
    uint32_t last_read_time;        // 上次读取时间
    uint32_t error_count;           // 错误计数
    uint32_t success_count;         // 成功计数
} CH423S_Status_t;

/**
 * @brief CH423S模块信息结构体（只读）
 */
struct CH423S_InfoStru {
    const bool &initialized;        // 初始化状态
    const uint16_t &outputState;    // 当前输出状态
    const uint8_t &gpioState;       // 当前GPIO状态
    const uint32_t &errorCount;     // 错误计数
    const uint32_t &successCount;   // 成功计数
};

/** @brief CH423S模块全局只读状态变量 */
extern const CH423S_InfoStru ch423sInfo;

// 时序配置参数
#define CH423S_DELAY_US      5       // I2C时序延时(微秒)
#define CH423S_TIMEOUT_MS    100     // 通信超时(毫秒)
#define CH423S_MAX_RETRY     3       // 最大重试次数

/**
 * @brief 初始化CH423S模块
 * @details 配置GPIO引脚，初始化状态
 * @return true 初始化成功，false 初始化失败
 */
bool ch423sInit();

/**
 * @brief 测试CH423S通信
 * @details 测试与CH423S的基本通信是否正常
 * @return CH423S_Result_t 通信结果
 */
CH423S_Result_t ch423sTestCommunication();

/**
 * @brief 同步设置输出状态
 * @param value 16位输出值 (OC0-OC15)
 * @return CH423S_Result_t 操作结果
 */
CH423S_Result_t ch423sSetOutput(uint16_t value);

/**
 * @brief 同步设置GPIO输出
 * @param value 8位GPIO输出值 (IO0-IO7)
 * @return CH423S_Result_t 操作结果
 */
CH423S_Result_t ch423sSetGpio(uint8_t value);

/**
 * @brief 同步读取GPIO状态
 * @param result 读取结果存储指针
 * @return CH423S_Result_t 操作结果
 */
CH423S_Result_t ch423sReadGpio(uint8_t* result);

/**
 * @brief 获取当前GPIO状态缓存
 * @return 8位GPIO状态值
 */
uint8_t ch423sGetGpioState();

/**
 * @brief 获取当前输出状态缓存
 * @return 16位输出状态值
 */
uint16_t ch423sGetOutputState();

/**
 * @brief 设置单个输出引脚
 * @param pin 引脚号 (0-15)
 * @param state 引脚状态 (true=高电平, false=低电平)
 * @return CH423S_Result_t 操作结果
 */
CH423S_Result_t ch423sSetPin(uint8_t pin, bool state);

/**
 * @brief 设置单个GPIO引脚
 * @param pin GPIO引脚号 (0-7)
 * @param state 引脚状态 (true=高电平, false=低电平)
 * @return CH423S_Result_t 操作结果
 */
CH423S_Result_t ch423sSetGpioPin(uint8_t pin, bool state);

/**
 * @brief 读取单个GPIO引脚状态
 * @param pin GPIO引脚号 (0-7)
 * @return 引脚状态 (true=高电平, false=低电平)
 */
bool ch423sGetGpioPin(uint8_t pin);

/**
 * @brief 复位CH423S设备
 * @return CH423S_Result_t 操作结果
 */
CH423S_Result_t ch423sReset();

/**
 * @brief CH423S任务处理函数
 * @details 由任务调度器调用，定期读取GPIO状态
 * @return 无
 */
void ch423sTask();

/**
 * @brief 获取错误计数
 * @return 累计错误次数
 */
uint32_t ch423sGetErrorCount();

/**
 * @brief 获取成功计数
 * @return 累计成功次数
 */
uint32_t ch423sGetSuccessCount();

/**
 * @brief 清除错误计数
 * @return 无
 */
void ch423sClearErrorCount();

/**
 * @brief 获取结果描述字符串
 * @param result 结果代码
 * @return 结果描述字符串
 */
const char* ch423sGetResultString(CH423S_Result_t result);

/**
 * @brief I2C设备扫描任务
 * @details 扫描I2C总线上的CH423S设备
 * @return 无
 */
void ch423sI2cScanTask(void);

/**
 * @brief CH423S设备检测任务
 * @details 定期检测CH423S设备是否在线
 * @return 无
 */
void ch423sDetectionTask(void);

/**
 * @brief 获取CH423S检测状态
 * @return true 设备已检测到，false 设备未检测到
 */
bool ch423sIsDetected(void);

/**
 * @brief 初始化CH423S检测模块
 * @return true 初始化成功，false 初始化失败
 */
bool ch423sDetectionInit(void);

/**
 * @brief CH423S模块任务
 * @note 包含设备检测和状态监控，需要定期调用
 */
void ch423sModuleTask(void);