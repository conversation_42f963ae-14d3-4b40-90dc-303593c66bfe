/* 程序头 */
#include "Arduino.h"
#include "logF.h"
#include "taskF.h"
#include "ioF.h"
#include "chipF.h"
#include "ch423sF.h"
#include "keyF.h"
#include "uartF.h"
#include "hubF.h"
#include "RTTStream.h"

// 外部RTT对象
extern RTTStream rtt;

/* 初始化 */
void setup()
{
    // 延时确保系统稳定
    delay(100);

    // 初始化日志模块
    logInit();    // 日志模块

    // 延时确保日志模块稳定
    delay(50);

    LOG_I("MAIN", "模块化系统启动");
    LOG_I("MAIN", "CH423S检测: PA6(SCL), PA7(SDA)");

    // 初始化各个模块
    ioInit();     // IO模块 (内部注册LedBlink任务)
    chipInit();   // 芯片模块 (内部注册MemCheck任务)
    hubInit();    // 指令处理中心

    // 注册CH423S模块任务 - 提高频率到2ms (500Hz) 加快LED响应
    taskRegister("CH423Module", 2, ch423sModuleTask, true);

    // 注册键盘模块任务 - 5ms扫描频率
    taskRegister("KeyModule", 5, keyModuleTask, true);

    // 注册串口模块任务 - 2ms处理频率 (超高速响应)
    taskRegister("UartModule", 2, uartModuleTask, true);

    // 建立键盘和串口模块之间的连接
    keySetUartCallbacks(uartAddPressedKey, uartRemovePressedKey);

    // 延时确保任务注册完成
    delay(10);

    LOG_I("MAIN", "模块化系统启动完成");
}

/* 主循环 */
void loop()
{
    // 运行任务调度器
    taskScheduler();
}