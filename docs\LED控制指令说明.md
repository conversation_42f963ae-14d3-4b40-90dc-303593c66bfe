# LED控制指令说明

## 🎯 **功能概述**

通过RTT和串口发送JSON格式指令，实时控制CH423S的64个按钮LED状态和亮度。
**无需按回车键！** 输入完整JSON后自动执行。

## 📝 **指令格式**

### LED控制格式
```json
{"cmd":"led","k01":"1"}
```

### 亮度控制格式
```json
{"cmd":"brightness","level":"2"}
```

### 参数说明

#### LED控制参数

##### 按钮编号 (k01-k65)
- `k01` - `k64`: 对应64个矩阵按钮
- `k65`: 独立按钮
- 按钮编号从1开始，对应物理按钮位置
- 映射关系：
  - k01-k08: 第1行 (按钮索引0-7)
  - k09-k16: 第2行 (按钮索引8-15)
  - k17-k24: 第3行 (按钮索引16-23)
  - ...
  - k57-k64: 第8行 (按钮索引56-63)
  - k65: 独立按钮

##### LED状态值
- `"0"`: 关闭LED
- `"1"`: 点亮红色LED
- `"2"`: 点亮绿色LED
- `"3"`: 点亮红绿双色LED

#### 亮度控制参数

##### 亮度级别 (level)
- `"0"`: 低亮度
- `"1"`: 中等亮度
- `"2"`: 高亮度
- `"3"`: 最高亮度

##### 其他参数
- `"query":"1"`: 查询当前亮度级别
- `"test":"1"`: 执行亮度测试

## 🚀 **使用示例**

### LED基本控制
```json
{"cmd":"led","k01":"1"}    // 点亮按钮1的红色LED
{"cmd":"led","k01":"0"}    // 关闭按钮1的LED
{"cmd":"led","k32":"2"}    // 点亮按钮32的绿色LED
{"cmd":"led","k64":"3"}    // 点亮按钮64的红绿双色LED
{"cmd":"led","k65":"1"}    // 点亮K65独立按钮的红色LED
```

### 亮度控制
```json
{"cmd":"brightness","level":"0"}    // 设置低亮度
{"cmd":"brightness","level":"1"}    // 设置中等亮度
{"cmd":"brightness","level":"2"}    // 设置高亮度
{"cmd":"brightness","level":"3"}    // 设置最高亮度
{"cmd":"brightness","query":"1"}    // 查询当前亮度级别
{"cmd":"brightness","test":"1"}     // 执行亮度测试
```

### 批量控制示例
```json
{"k01":"1"}    // 左上角红色
{"k08":"2"}    // 右上角绿色
{"k57":"3"}    // 左下角双色
{"k64":"1"}    // 右下角红色
```

### 创建图案
```json
// 创建十字图案
{"k28":"1"}    // 中心上方
{"k29":"1"}    // 中心上方
{"k35":"1"}    // 中心左侧
{"k36":"1"}    // 中心
{"k37":"1"}    // 中心右侧
{"k38":"1"}    // 中心右侧
{"k44":"1"}    // 中心下方
{"k45":"1"}    // 中心下方
```

## 🔧 **技术实现**

### JSON解析
- 支持简单的单键值对JSON格式
- 自动去除空格和换行符
- 错误格式会显示警告信息

### 指令处理流程
1. **实时接收**: RTT逐字符接收JSON指令
2. **自动触发**: 收到 `}` 字符时立即处理
3. **解析**: 提取按钮编号和LED状态
4. **验证**: 检查参数有效性
5. **执行**: 调用CH423S API设置LED
6. **反馈**: 输出执行结果日志

### 🚀 **实时响应特性**
- **无需回车**: 输入 `{"k01":"1"}` 后立即执行
- **超快响应**: 200ms超时，极速处理
- **即时反馈**: LED状态立即改变

### 错误处理
- 无效按钮编号: `无效的按钮编号: k99`
- 无效LED状态: `无效的LED状态: 5`
- JSON格式错误: `JSON解析失败: {k01:1}`

## 📊 **按钮映射表**

```
行1: k01 k02 k03 k04 k05 k06 k07 k08
行2: k09 k10 k11 k12 k13 k14 k15 k16
行3: k17 k18 k19 k20 k21 k22 k23 k24
行4: k25 k26 k27 k28 k29 k30 k31 k32
行5: k33 k34 k35 k36 k37 k38 k39 k40
行6: k41 k42 k43 k44 k45 k46 k47 k48
行7: k49 k50 k51 k52 k53 k54 k55 k56
行8: k57 k58 k59 k60 k61 k62 k63 k64
```

## 🎮 **实际使用**

### 通过RTT Viewer
1. 连接STM32设备
2. 打开RTT Viewer
3. 在输入框中输入JSON指令
4. **无需按回车！** 输入完 `}` 后自动执行
5. 观察LED变化和日志反馈

### 指令示例序列 (无需回车)
```json
{"k01":"1"}    // 输入完}后立即点亮左上角
{"k08":"2"}    // 输入完}后立即点亮右上角绿色
{"k57":"3"}    // 输入完}后立即点亮左下角双色
{"k64":"1"}    // 输入完}后立即点亮右下角红色
{"k01":"0"}    // 输入完}后立即关闭左上角
```

## 🔍 **调试信息**

### 成功日志
```
[12345] INFO [CMD] 设置按钮k01(1) LED状态: 1
```

### 错误日志
```
[12345] WARN [CMD] 无效的按钮编号: k99
[12345] WARN [CMD] 无效的LED状态: 5
[12345] WARN [INPUT] JSON解析失败: {k01:1}
```

## 💡 **使用技巧**

1. **快速测试**: 使用k01-k08测试第一行LED
2. **边框效果**: 使用k01,k08,k57,k64创建四角效果
3. **中心十字**: 使用k28,k29,k36,k37,k44,k45
4. **逐行点亮**: 依次发送k01-k08, k09-k16等
5. **清除所有**: 依次发送所有按钮的"0"状态

## 🚨 **注意事项**

1. JSON格式必须严格，使用双引号
2. 按钮编号必须是k01-k64格式
3. LED状态值必须是字符串"0"-"3"
4. 指令发送后会立即生效
5. 系统会自动以1ms间隔更新显示

现在你可以通过RTT发送JSON指令来实时控制LED了！
