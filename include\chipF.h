#pragma once
#include <stdint.h>

/**
 * @file chipF.h
 * @brief 芯片功能模块头文件
 * @details 提供芯片相关功能，包括内存监控、芯片信息查询等
 *
 * 使用示例：
 *
 * 1. 初始化芯片模块：
 *    chipInit();
 *
 * 2. 获取内存信息：
 *    uint32_t freeMemory = chipGetFreeMemory();
 *    uint8_t usage = chipGetMemoryUsage();
 *
 * 3. 查询芯片状态：
 *    bool state = chipInfo.initialized;
 *    uint32_t memory = chipInfo.freeMemory;
 */

/**
 * @brief 芯片模块信息结构体
 * @details 提供芯片状态查询
 */
struct ChipInfo {
    const bool &initialized;       /**< 模块是否已初始化 */
    const uint32_t &freeMemory;    /**< 当前剩余内存 */
    const uint32_t &totalMemory;   /**< 总内存大小 */
    const uint8_t &memoryUsage;      /**< 内存使用率(百分比) */
};

/** @brief 芯片模块全局状态变量 */
extern const ChipInfo chipInfo;

/**
 * @brief 初始化芯片模块
 * @details 初始化芯片相关功能，注册内存监控任务到调度器
 * @return 无
 */
void chipInit();

/**
 * @brief 获取剩余内存
 * @return 剩余内存字节数
 */
uint32_t chipGetFreeMemory();

/**
 * @brief 获取总内存大小
 * @return 总内存字节数
 */
uint32_t chipGetTotalMemory();

/**
 * @brief 获取内存使用率
 * @return 内存使用率(百分比，0-100)
 */
uint8_t chipGetMemoryUsage();

/**
 * @brief 内存检查任务
 * @details 定期检查内存使用情况，发出警告，由任务调度器调用
 * @return 无
 */
void taskMemoryCheck();

// 定时器相关函数已移除，CH423S使用同步模式
