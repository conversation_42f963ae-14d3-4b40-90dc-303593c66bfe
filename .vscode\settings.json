{"files.associations": {"array": "cpp", "deque": "cpp", "string": "cpp", "unordered_map": "cpp", "vector": "cpp", "string_view": "cpp", "initializer_list": "cpp", "random": "cpp", "*.tcc": "cpp", "ostream": "cpp", "algorithm": "cpp", "system_error": "cpp", "functional": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "newlib.h": "c", "_newlib_version.h": "c", "sstream": "cpp", "compare": "cpp", "cmath": "cpp", "pinconfigured.h": "c", "lv_version.h": "c", "bitset": "cpp", "regex": "cpp", "DEMO_LED.C": "cpp", "DEMOAUTO.C": "cpp", "CH423IF.C": "cpp"}}