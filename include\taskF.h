#pragma once
#include <stdint.h>

/**
 * @file taskF.h
 * @brief 任务调度器模块头文件
 * @details 提供非阻塞的任务调度功能，支持多任务异步执行
 *
 * 使用示例：
 *
 * 1. 注册任务：
 *    taskRegister("MyTask", 1000, myTaskFunction, true);
 *
 * 2. 运行调度器：
 *    void loop() {
 *        taskScheduler();
 *    }
 *
 * 3. 控制任务：
 *    taskEnable("MyTask", false);  // 禁用任务
 *    taskSetInterval("MyTask", 2000);  // 设置间隔为2秒
 */

/**
 * @brief 任务结构体定义
 * @details 存储单个任务的所有信息
 */
struct Task {
    uint32_t interval;      /**< 执行间隔(毫秒) */
    uint32_t lastRun;       /**< 上次执行时间 */
    void (*function)();     /**< 任务函数指针 */
    bool enabled;           /**< 任务是否启用 */
    const char* name;       /**< 任务名称(用于调试) */
};

/**
 * @brief 最大任务数量定义
 * @details 可根据系统资源调整
 */
#define MAX_TASKS 8

/**
 * @brief 任务调度器信息结构体
 * @details 提供调度器状态查询
 */
struct TaskSchedulerInfo {
    const uint8_t &taskCount;       /**< 当前注册的任务数量 */
    const bool &isRunning;          /**< 调度器是否运行中 */
    const uint32_t &totalRuns;      /**< 总执行次数 */
};

/** @brief 任务调度器全局状态变量 */
extern const TaskSchedulerInfo taskInfo;



/**
 * @brief 注册新任务
 * @param name 任务名称
 * @param interval 执行间隔(毫秒)
 * @param function 任务函数指针
 * @param enabled 是否立即启用
 * @return true 注册成功，false 注册失败(任务数量已满)
 */
bool taskRegister(const char* name, uint32_t interval, void (*function)(), bool enabled = true);

/**
 * @brief 运行任务调度器
 * @details 在主循环中调用，检查并执行到期的任务
 * @return 无
 */
void taskScheduler();

/**
 * @brief 启用或禁用任务
 * @param taskName 任务名称
 * @param enable true启用，false禁用
 * @return true 操作成功，false 任务不存在
 */
bool taskEnable(const char* taskName, bool enable);

/**
 * @brief 设置任务执行间隔
 * @param taskName 任务名称
 * @param newInterval 新的执行间隔(毫秒)
 * @return true 操作成功，false 任务不存在
 */
bool taskSetInterval(const char* taskName, uint32_t newInterval);

/**
 * @brief 获取任务信息
 * @param taskName 任务名称
 * @param task 输出参数，存储任务信息
 * @return true 获取成功，false 任务不存在
 */
bool taskGetInfo(const char* taskName, Task* task);

/**
 * @brief 列出所有任务
 * @details 通过日志输出所有任务的状态信息
 * @return 无
 */
void taskListAll();

/**
 * @brief 获取调度器统计信息
 * @return TaskSchedulerInfo 调度器信息结构体
 */
const TaskSchedulerInfo& taskGetSchedulerInfo();