/**
 * @file hubF.cpp
 * @brief 指令处理中心模块实现文件
 * @details 负责接收、解析和分发各种JSON指令
 */

#include "hubF.h"
#include "logF.h"
#include "ch423sF.h"
#include "Arduino.h"
#include <string.h>

// 模块标识
static const char* TAG = "HUB";

// 模块状态
static bool hub_initialized = false;

// ==================== 内部函数声明 ====================

static CommandType_t parseCommandType(const char* json);
static void processLedCommand(const char* json);
static bool parseJsonKeyValue(const char* json, const char* key, char* value, size_t valueSize);
static uint8_t parseButtonIndex(const char* key);

// ==================== JSON解析工具函数 ====================

/**
 * @brief 从JSON中提取指定键的值 (支持字符串和数字格式)
 * @param json JSON字符串
 * @param key 要查找的键名
 * @param value 输出值的缓冲区
 * @param valueSize 缓冲区大小
 * @return true 解析成功，false 解析失败
 */
static bool parseJsonKeyValue(const char* json, const char* key, char* value, size_t valueSize) {
    if (!json || !key || !value || valueSize == 0) return false;

    // 构造搜索模式 "key":
    char pattern[32];
    snprintf(pattern, sizeof(pattern), "\"%s\":", key);

    // 查找键名
    const char* keyPos = strstr(json, pattern);
    if (!keyPos) return false;

    // 移动到值的开始位置
    const char* valueStart = keyPos + strlen(pattern);

    // 跳过空白字符
    while (*valueStart == ' ' || *valueStart == '\t') {
        valueStart++;
    }

    const char* valueEnd = NULL;

    if (*valueStart == '"') {
        // 字符串格式: "key":"value"
        valueStart++;  // 跳过开始的引号
        valueEnd = strchr(valueStart, '"');
        if (!valueEnd) return false;
    } else {
        // 数字格式: "key":123
        valueEnd = valueStart;
        while (*valueEnd >= '0' && *valueEnd <= '9') {
            valueEnd++;
        }
        if (valueEnd == valueStart) return false;  // 没有找到数字
    }

    // 提取值
    size_t valueLen = valueEnd - valueStart;
    if (valueLen >= valueSize) return false;

    strncpy(value, valueStart, valueLen);
    value[valueLen] = '\0';

    return true;
}

/**
 * @brief 解析按钮编号 (k1-k65)
 * @param key 按钮键名 (如 "k1", "k65")
 * @return 按钮索引 (0-64)，失败返回255
 */
static uint8_t parseButtonIndex(const char* key) {
    if (!key || key[0] != 'k') {
        return 255;  // 无效
    }

    // 解析数字部分 (支持k1到k65)
    const char* numStr = key + 1;  // 跳过'k'
    if (strlen(numStr) == 0 || strlen(numStr) > 2) {
        return 255;  // 无效长度
    }

    // 检查所有字符都是数字
    for (int i = 0; numStr[i] != '\0'; i++) {
        if (numStr[i] < '0' || numStr[i] > '9') {
            return 255;  // 包含非数字字符
        }
    }

    // 转换为数字
    uint8_t num = (uint8_t)atoi(numStr);
    if (num < 1 || num > 65) {  // 支持k1-k65
        return 255;  // 超出范围
    }

    uint8_t button_index = num - 1;  // 转换为0-64索引

    // 修复K31和K32的映射问题
    if (button_index == 30) {        // K31 → 映射到索引31
        button_index = 31;
    } else if (button_index == 31) { // K32 → 映射到索引30
        button_index = 30;
    }

    return button_index;
}

// ==================== 指令解析和分发 ====================

/**
 * @brief 解析指令类型
 * @param json JSON指令字符串
 * @return 指令类型
 */
static CommandType_t parseCommandType(const char* json) {
    char cmdType[16] = {0};

    if (parseJsonKeyValue(json, "cmd", cmdType, sizeof(cmdType))) {
        if (strcmp(cmdType, "led") == 0) {
            return CMD_LED;
        } else if (strcmp(cmdType, "brightness") == 0) {
            return CMD_BRIGHTNESS;
        }
    }

    return CMD_UNKNOWN;
}

/**
 * @brief 处理LED控制指令
 * @param json JSON指令字符串 (格式: {"cmd":"led","k1":"1"})
 */
static void processLedCommand(const char* json) {
    // 遍历可能的按钮编号，查找LED控制指令
    for (uint8_t i = 1; i <= 65; i++) {
        char buttonKey[8];
        char ledValue[8] = {0};

        // 构造按钮键名 (k1, k2, ..., k65) - 不补零
        snprintf(buttonKey, sizeof(buttonKey), "k%d", i);

        // 尝试从JSON中提取该按钮的值
        if (parseJsonKeyValue(json, buttonKey, ledValue, sizeof(ledValue))) {
            // 解析按钮索引
            uint8_t buttonIndex = parseButtonIndex(buttonKey);
            if (buttonIndex == 255) {
                LOG_W(TAG, "无效的按钮编号: %s", buttonKey);
                continue;
            }

            // 解析LED状态
            uint8_t ledState = 0;
            if (strcmp(ledValue, "0") == 0) {
                ledState = 0;  // 关闭
            } else if (strcmp(ledValue, "1") == 0) {
                ledState = 1;  // 红色
            } else if (strcmp(ledValue, "2") == 0) {
                ledState = 2;  // 绿色
            } else if (strcmp(ledValue, "3") == 0) {
                ledState = 3;  // 红绿双色
            } else {
                LOG_W(TAG, "无效的LED状态: %s", ledValue);
                continue;
            }

            // 执行LED控制
            if (buttonIndex == 64) {  // K65特殊处理
                ch423sSetK65ButtonState(ledState);
                LOG_I(TAG, "设置K65按钮 LED状态: %s", ledValue);
            } else {
                ch423sSetButtonState(buttonIndex, ledState);
                uint8_t row = buttonIndex / 8;
                uint8_t col = buttonIndex % 8;
                LOG_I(TAG, "设置按钮%s(索引%d,行%d,列%d) LED状态: %s",
                      buttonKey, buttonIndex, row, col, ledValue);
            }

            // 找到一个有效的按钮控制指令就返回
            return;
        }
    }

    LOG_W(TAG, "LED指令中未找到有效的按钮控制: %s", json);
}

/**
 * @brief 处理亮度控制指令
 * @param json JSON指令字符串 (格式: {"cmd":"brightness","level":"2"})
 */
static void processBrightnessCommand(const char* json) {
    char levelValue[8] = {0};
    char queryValue[8] = {0};
    char testValue[8] = {0};

    // 检查是否为亮度设置指令
    if (parseJsonKeyValue(json, "level", levelValue, sizeof(levelValue))) {
        // 解析亮度级别
        uint8_t level = 0;
        if (strcmp(levelValue, "0") == 0) {
            level = 0;  // 低亮度
        } else if (strcmp(levelValue, "1") == 0) {
            level = 1;  // 中等亮度
        } else if (strcmp(levelValue, "2") == 0) {
            level = 2;  // 高亮度
        } else if (strcmp(levelValue, "3") == 0) {
            level = 3;  // 最高亮度
        } else {
            LOG_W(TAG, "无效的亮度级别: %s", levelValue);
            return;
        }

        // 执行亮度设置
        if (ch423sSetBrightness((ch423s_brightness_level_t)level)) {
            LOG_I(TAG, "亮度设置成功: 级别%d", level);
        } else {
            LOG_E(TAG, "亮度设置失败: 级别%d", level);
        }
        return;
    }

    // 检查是否为亮度查询指令
    if (parseJsonKeyValue(json, "query", queryValue, sizeof(queryValue))) {
        if (strcmp(queryValue, "1") == 0) {
            ch423s_brightness_level_t currentLevel = ch423sGetBrightness();
            LOG_I(TAG, "当前亮度级别: %d", currentLevel);
        } else {
            LOG_W(TAG, "无效的查询参数: %s", queryValue);
        }
        return;
    }

    // 检查是否为亮度测试指令
    if (parseJsonKeyValue(json, "test", testValue, sizeof(testValue))) {
        if (strcmp(testValue, "1") == 0) {
            LOG_I(TAG, "开始执行亮度测试...");
            ch423sTestBrightness();
            LOG_I(TAG, "亮度测试完成");
        } else {
            LOG_W(TAG, "无效的测试参数: %s", testValue);
        }
        return;
    }

    LOG_W(TAG, "亮度指令中未找到有效的参数: %s", json);
}

// ==================== 公共API函数 ====================

/**
 * @brief 处理JSON指令
 * @param jsonCommand JSON格式的指令字符串
 */
void hubProcessCommand(const char* jsonCommand) {
    if (!jsonCommand) {
        LOG_W(TAG, "指令为空");
        return;
    }

    if (!hub_initialized) {
        LOG_W(TAG, "指令处理中心未初始化");
        return;
    }

    LOG_D(TAG, "处理指令: %s", jsonCommand);

    // 解析指令类型
    CommandType_t cmdType = parseCommandType(jsonCommand);

    // 根据类型分发处理
    switch(cmdType) {
        case CMD_LED:
            processLedCommand(jsonCommand);
            break;

        case CMD_BRIGHTNESS:
            processBrightnessCommand(jsonCommand);
            break;

        case CMD_UNKNOWN:
        default:
            LOG_W(TAG, "未知指令类型: %s", jsonCommand);
            break;
    }
}

/**
 * @brief 初始化指令处理中心
 */
bool hubInit(void) {
    if (hub_initialized) {
        LOG_W(TAG, "指令处理中心已经初始化");
        return true;
    }

    hub_initialized = true;

    LOG_I(TAG, "指令处理中心初始化完成");
    LOG_I(TAG, "支持的指令格式:");
    LOG_I(TAG, "  LED控制: {\"cmd\":\"led\",\"k1\":\"1\"}");
    LOG_I(TAG, "  亮度控制: {\"cmd\":\"brightness\",\"level\":\"2\"}");
    LOG_I(TAG, "  亮度查询: {\"cmd\":\"brightness\",\"query\":\"1\"}");
    LOG_I(TAG, "  亮度测试: {\"cmd\":\"brightness\",\"test\":\"1\"}");

    return true;
}

/**
 * @brief 获取指令处理中心初始化状态
 */
bool hubIsInitialized(void) {
    return hub_initialized;
}