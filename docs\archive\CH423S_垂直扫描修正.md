# CH423S垂直扫描修正说明

## 🎯 **关键发现**

你的观察非常正确！**CH423S的自动扫描功能就是垂直扫描的，这是硬件设计决定的，我们无法改变。**

## 🔧 **CH423S自动扫描工作原理**

### 硬件扫描机制
- **DIG0-DIG7**: 控制列扫描（垂直方向），芯片自动循环激活
- **OC0-OC15**: 提供行数据（每列中哪些LED点亮）
- **扫描频率**: 芯片内部自动控制，无需软件干预

### 正确的数据映射
```cpp
// 修正前（错误的行扫描思维）
for (uint8_t row = 0; row < 8; row++) {
    for (uint8_t col = 0; col < 8; col++) {
        // 错误：按行组织数据
    }
}

// 修正后（正确的列扫描映射）
for (uint8_t col = 0; col < 8; col++) {  // 遍历每一列
    for (uint8_t row = 0; row < 8; row++) {  // 该列的每一行
        uint8_t state = button_led_map[row][col];
        if (state == 1 || state == 3) red_data |= (1 << row);
    }
    ch423s_display_buffer[col] = red_data;      // DIG0-DIG7
    ch423s_display_buffer[col + 8] = green_data; // DIG8-DIG15
}
```

## 📊 **寄存器映射关系**

### 显示缓冲区布局
- `ch423s_display_buffer[0]`: DIG0寄存器 - 第0列的红色LED数据
- `ch423s_display_buffer[1]`: DIG1寄存器 - 第1列的红色LED数据
- ...
- `ch423s_display_buffer[7]`: DIG7寄存器 - 第7列的红色LED数据
- `ch423s_display_buffer[8]`: DIG8寄存器 - 第0列的绿色LED数据
- ...
- `ch423s_display_buffer[15]`: DIG15寄存器 - 第7列的绿色LED数据

### 位映射关系
每个寄存器的8位对应该列的8行LED：
- bit0: 第0行LED
- bit1: 第1行LED
- ...
- bit7: 第7行LED

## 🔄 **修正的测试流程**

### 阶段0: 基本输出测试
测试CH423S的开漏输出功能

### 阶段1: 逐列测试
```cpp
// 每次点亮一整列，验证列扫描
for (uint8_t col = 0; col < 8; col++) {
    for (uint8_t row = 0; row < 8; row++) {
        ch423sSetButtonLed(row, col, 1);
    }
    delay(2000);  // 观察哪一列被点亮
}
```

### 阶段2: 垂直累积扫描
```cpp
// 在每列内从上到下累积点亮
for (uint8_t col = 0; col < 8; col++) {
    for (uint8_t row = 0; row <= current_row; row++) {
        ch423sSetButtonLed(row, col, 1);
    }
}
```

## 💡 **为什么之前的映射不正确**

### 错误的假设
我们之前假设CH423S是行扫描的：
- DIG0-DIG7控制行
- 每个寄存器控制一行的8个LED

### 正确的理解
CH423S实际是列扫描的：
- DIG0-DIG7控制列
- 每个寄存器控制一列的8个LED
- 芯片自动循环激活DIG0到DIG7

## 🎯 **预期效果**

修正后，你应该看到：
1. **阶段1**: 8次，每次一整列LED点亮（从左到右或从右到左）
2. **阶段2**: 每列内从上到下（或从下到上）累积点亮

## 🔍 **验证方法**

通过观察LED的点亮模式：
- 如果看到垂直的LED条纹 → 列扫描正确
- 如果看到水平的LED条纹 → 可能需要调整行列映射
- 如果LED位置仍然混乱 → 可能需要调整位序或寄存器地址

## 📝 **总结**

这次的发现很重要：
1. **硬件限制**: CH423S的扫描方式是固定的，不能改变
2. **软件适配**: 我们的代码必须适配硬件的扫描方式
3. **映射关系**: 正确理解列扫描的数据组织方式

现在的代码应该能正确地配合CH423S的垂直扫描机制工作！
