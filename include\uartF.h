/**
 * @file uartF.h
 * @brief 串口通信模块头文件
 * @details 处理串口通信、命令解析、JSON数据发送
 */

#ifndef UARTF_H
#define UARTF_H

#include "Arduino.h"
#include <stdint.h>
#include <stdbool.h>

// 串口配置
#define UART_BAUD_RATE      115200
#define UART_BUFFER_SIZE    128

// 组合按键管理
#define MAX_PRESSED_KEYS    4

/**
 * @brief 按键组合结构
 */
typedef struct {
    uint8_t key_id;
    uint32_t press_time;
} PressedKey_t;

/**
 * @brief 初始化串口模块
 * @return true 初始化成功
 * @return false 初始化失败
 */
bool uartInit(void);

/**
 * @brief 串口接收处理任务
 * @note 需要定期调用，建议10ms间隔
 */
void uartReceiveTask(void);

/**
 * @brief 发送按键组合JSON数据
 * @param pressed_keys 按下的按键数组
 * @param count 按键数量
 */
void uartSendKeyCombo(const PressedKey_t* pressed_keys, uint8_t count);

/**
 * @brief 添加按键到组合列表
 * @param key_id 按键ID
 */
void uartAddPressedKey(uint8_t key_id);

/**
 * @brief 从组合列表移除按键
 * @param key_id 按键ID
 */
void uartRemovePressedKey(uint8_t key_id);

/**
 * @brief 获取当前按下的按键数量
 * @return uint8_t 按键数量
 */
uint8_t uartGetPressedCount(void);

/**
 * @brief 获取串口模块初始化状态
 * @return true 已初始化
 * @return false 未初始化
 */
bool uartIsInitialized(void);

/**
 * @brief 串口模块任务
 * @note 包含串口接收处理，需要定期调用
 */
void uartModuleTask(void);

/**
 * @brief 串口连接测试函数 (调试用)
 */
void uartTestConnection(void);

#endif // UARTF_H