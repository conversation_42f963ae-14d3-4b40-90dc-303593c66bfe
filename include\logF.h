#pragma once
#include <stdint.h>
#include <stdarg.h>

/**
 * @file logF.h
 * @brief 日志模块头文件
 * @details 提供日志分级打印功能，支持不同级别的日志输出控制
 *
 * 使用示例：
 *
 * 1. 打印不同级别日志：
 *    LOG_E("TAG", "错误信息: %d", errCode);
 *    LOG_W("TAG", "警告信息");
 *    LOG_I("TAG", "初始化完成");
 *    LOG_D("TAG", "调试信息: %s", msg);
 *
 * 2. 设置日志级别：
 *    logSetLevel(LOG_INFO); // 只显示INFO及以上级别日志
 *
 * 3. 在任务中初始化并使用日志：
 *    xTaskCreate(logTask, "log", 512, NULL, 1, NULL);
 *    // ... 其他任务 ...
 */

/**
 * @brief 日志级别定义
 * @note 日志级别从低到高，越低级别越重要，必须打印
 */
#define LOG_OFF 0   /**< 关闭所有日志输出，不打印任何信息 */
#define LOG_ERROR 1 /**< 错误级别日志，程序错误时打印 */
#define LOG_WARN 2  /**< 警告级别日志，可能存在问题时打印 */
#define LOG_INFO 3  /**< 信息级别日志，程序正常状态信息 */
#define LOG_DEBUG 4 /**< 调试级别日志，开发调试时使用 */

/**
 * @brief 定义默认日志级别
 * @details 默认为警告级别，确保错误和警告日志都会打印
 */
#define LOG_DEFAULT_LEVEL LOG_WARN

/**
 * @brief 只读日志信息结构体
 * @details 存储日志相关信息，提供给外部模块读取日志状态
 */
struct logInfoStru
{
    // 状态标志
    const bool &initFlag;    /**< 初始化标志，true表示模块已初始化 */
    const uint8_t &logLevel; /**< 当前日志级别，用于查询 */
};

/** @brief 日志模块全局只读状态变量，用于外部模块查询日志状态 */
extern const logInfoStru logS;

/**
 * @brief 日志打印函数
 * @param level 日志级别
 * @param tag 日志标签
 * @param format 格式化字符串
 * @param ... 可变参数
 * @details 根据日志级别打印带颜色的格式化日志信息
 * @return 无
 */
void logPrint(uint8_t level, const char *tag, const char *format, ...);

/**
 * @brief 设置日志打印级别
 * @param level 要设置的日志级别
 * @details 设置日志级别，只显示大于等于该级别的日志，LOG_OFF将关闭所有日志输出
 * @return 无
 */
void logSetLevel(uint8_t level);

/**
 * @brief 日志快捷宏定义
 * @details 便于在代码中快速使用不同级别的日志打印
 */
#define LOG_E(tag, ...) logPrint(LOG_ERROR, tag, __VA_ARGS__) /**< 错误级别日志 */
#define LOG_W(tag, ...) logPrint(LOG_WARN, tag, __VA_ARGS__)  /**< 警告级别日志 */
#define LOG_I(tag, ...) logPrint(LOG_INFO, tag, __VA_ARGS__)  /**< 信息级别日志 */
#define LOG_D(tag, ...) logPrint(LOG_DEBUG, tag, __VA_ARGS__) /**< 调试级别日志 */

/**
 * @brief 初始化日志模块
 * @details 初始化RTT和日志系统，注册LogProcess任务
 * @return 无
 */
void logInit();

/**
 * @brief 处理日志相关的输入命令
 * @details 处理RTT输入的日志控制命令，由taskF调度器调用
 * @return 无
 */
void logProcessInput();