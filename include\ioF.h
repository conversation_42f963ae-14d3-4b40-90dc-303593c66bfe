#pragma once
#include <stdint.h>

/**
 * @file ioF.h
 * @brief IO功能模块头文件
 * @details 提供GPIO控制、LED管理等IO相关功能
 *
 * 使用示例：
 *
 * 1. 初始化IO模块：
 *    ioInit();
 *
 * 2. 控制LED：
 *    ioLedSet(true);   // 打开LED
 *    ioLedSet(false);  // 关闭LED
 *    ioLedToggle();    // 切换LED状态
 *
 * 3. 查询LED状态：
 *    bool state = ioInfo.ledState;
 */

/**
 * @brief IO模块信息结构体
 * @details 提供IO状态查询
 */
struct IoInfo {
    const bool &initialized;       /**< 模块是否已初始化 */
    const bool &ledState;          /**< 当前LED状态 */
};

/** @brief IO模块全局状态变量 */
extern const IoInfo ioInfo;

/**
 * @brief 初始化IO模块
 * @details 初始化GPIO配置，注册LED相关任务到调度器
 * @return 无
 */
void ioInit();

/**
 * @brief 设置LED状态
 * @param state true为点亮，false为熄灭
 * @return 无
 */
void ioLedSet(bool state);

/**
 * @brief 切换LED状态
 * @details 如果LED当前是亮的则熄灭，如果是灭的则点亮
 * @return 无
 */
void ioLedToggle();

/**
 * @brief 获取LED当前状态
 * @return true LED点亮，false LED熄灭
 */
bool ioLedGetState();

/**
 * @brief LED闪烁任务
 * @details 控制PC13 LED闪烁，由任务调度器调用
 * @return 无
 */
void taskLedBlink();