# 🎯 CH423S系统架构与使用指南

## 📋 **系统概述**

CH423S控制系统是一个基于STM32的高性能LED矩阵控制系统，采用模块化架构设计，支持64位按钮LED控制和K65独立按钮控制。

### 🏗️ **核心特性**
- **64位按钮矩阵**: 8×8 LED矩阵，每个按钮支持红/绿/双色LED
- **K65独立按钮**: 额外的独立按钮控制
- **JSON指令格式**: 统一的指令接口
- **高速响应**: ~10ms级别的LED控制响应
- **模块化架构**: 易于扩展和维护

## 🏛️ **系统架构**

### 架构图
```
┌─────────────────────────────────────────────────────────┐
│                 指令处理架构 v2.0                        │
├─────────────┬─────────────┬─────────────────────────────┤
│   输入层    │   分发层    │         执行层              │
├─────────────┼─────────────┼─────────────────────────────┤
│ logF.cpp    │ hubF.cpp    │ ch423sF.cpp                 │
│ - RTT接收   │ - 指令解析  │ - LED矩阵控制               │
│ - 指令转发  │ - 类型分发  │ - K65按钮控制               │
│             │ - 错误处理  │                             │
│ uartF.cpp   │             │ (未来可扩展)                │
│ - 串口接收  │             │ - 系统控制                  │
│ - 指令转发  │             │ - 配置管理                  │
└─────────────┴─────────────┴─────────────────────────────┘
```

### 模块职责

#### 1. **输入层 (logF.cpp)**
- RTT输入接收和处理
- 指令完整性检查
- 转发给指令分发中心

#### 2. **分发层 (hubF.cpp)**
- JSON指令解析
- 指令类型识别
- 分发到对应执行模块
- 统一错误处理

#### 3. **执行层 (ch423sF.cpp)**
- CH423S硬件控制
- LED状态管理
- 高速数据更新

## 🎮 **指令格式**

### JSON指令结构
```json
{"cmd":"指令类型","参数":"值"}
```

### LED控制指令
```json
{"cmd":"led","k01":"1"}    // 点亮K01红色LED
{"cmd":"led","k32":"2"}    // 点亮K32绿色LED
{"cmd":"led","k65":"3"}    // 点亮K65双色LED
{"cmd":"led","k01":"0"}    // 关闭K01 LED
```

### 指令参数说明
- **cmd**: 指令类型，当前支持 `"led"`
- **k01-k64**: 矩阵按钮编号 (8×8矩阵)
- **k65**: 独立按钮编号
- **LED状态值**:
  - `"0"`: 关闭LED
  - `"1"`: 红色LED
  - `"2"`: 绿色LED
  - `"3"`: 红绿双色LED

## 📊 **按钮映射系统**

### 矩阵按钮映射 (K01-K64)
```
行\列  0   1   2   3   4   5   6   7
  0   K01 K02 K03 K04 K05 K06 K07 K08
  1   K09 K10 K11 K12 K13 K14 K15 K16
  2   K17 K18 K19 K20 K21 K22 K23 K24
  3   K25 K26 K27 K28 K29 K30 K31 K32
  4   K33 K34 K35 K36 K37 K38 K39 K40
  5   K41 K42 K43 K44 K45 K46 K47 K48
  6   K49 K50 K51 K52 K53 K54 K55 K56
  7   K57 K58 K59 K60 K61 K62 K63 K64
```

### 索引计算公式
```cpp
// 按钮编号转索引
uint8_t button_index = button_number - 1;  // K01 → 索引0

// 索引转行列
uint8_t row = button_index / 8;
uint8_t col = button_index % 8;
```

### K65独立按钮
- **硬件引脚**: PB1(红色), PB11(绿色)
- **控制方式**: 直接GPIO控制
- **支持状态**: 0=关闭, 1=红色, 2=绿色, 3=双色

## 🚀 **API接口**

### 核心控制函数
```cpp
// 设置单个按钮状态 (索引方式)
void ch423sSetButtonState(uint8_t buttonIndex, uint8_t state);

// 设置单个按钮状态 (行列方式)
void ch423sSetButtonLed(uint8_t row, uint8_t col, uint8_t state);

// 设置K65按钮状态
void ch423sSetK65ButtonState(uint8_t state);

// 批量设置所有按钮
void ch423sSetAllButtonStates(const uint8_t* states);

// 清除所有LED
void ch423sClearAllLeds(void);
```

### 状态查询函数
```cpp
// 获取单个按钮状态
uint8_t ch423sGetButtonState(uint8_t buttonIndex);

// 获取K65按钮状态
uint8_t ch423sGetK65ButtonState(void);

// 获取所有按钮状态
void ch423sGetAllButtonStates(uint8_t* states);
```

## 🧪 **使用示例**

### 基本LED控制
```json
// 点亮第一个按钮的红色LED
{"cmd":"led","k01":"1"}

// 点亮第32个按钮的绿色LED
{"cmd":"led","k32":"2"}

// 点亮K65按钮的双色LED
{"cmd":"led","k65":"3"}

// 关闭所有LED
{"cmd":"led","k01":"0"}
{"cmd":"led","k32":"0"}
{"cmd":"led","k65":"0"}
```

### 批量控制示例
```json
// 快速切换不同按钮
{"cmd":"led","k01":"1"}
{"cmd":"led","k02":"2"}
{"cmd":"led","k03":"3"}
{"cmd":"led","k04":"1"}
```

### 动态效果示例
```json
// 流水灯效果
{"cmd":"led","k01":"1"}  // 延时
{"cmd":"led","k01":"0"}
{"cmd":"led","k02":"1"}  // 延时
{"cmd":"led","k02":"0"}
{"cmd":"led","k03":"1"}
```

## ⚡ **性能特性**

### 响应时间
- **指令处理**: ~1ms (LogProcess任务频率)
- **LED更新**: ~10ms (CH423Module任务频率)
- **总响应时间**: < 20ms
- **视觉效果**: 几乎实时响应

### 系统资源
- **RAM使用**: ~16% (3308/20480 字节)
- **Flash使用**: ~45% (29392/65536 字节)
- **CPU负载**: 优化后保持在合理范围

### 任务调度频率
```cpp
LogProcess:   1000Hz (1ms间隔)  // 指令接收
CH423Module:  100Hz  (10ms间隔) // LED更新
KeyModule:    200Hz  (5ms间隔)  // 按键扫描
UartModule:   500Hz  (2ms间隔)  // 串口处理
```

## 🔧 **硬件连接**

### CH423S连接
- **SCL**: PA6 (I2C时钟线)
- **SDA**: PA7 (I2C数据线)
- **电源**: 3.3V/5V
- **上拉电阻**: SCL/SDA各需4.7kΩ到VCC

### K65按钮连接
- **红色LED**: PB1 (低电平点亮)
- **绿色LED**: PB11 (低电平点亮)

### LED矩阵连接
- **OC0-OC15**: CH423S开漏输出，连接LED矩阵
- **DIG0-DIG15**: 显示寄存器，用于自动扫描

## 📈 **扩展能力**

### 未来支持的指令类型
```json
{"cmd":"sys","reset":"1"}      // 系统控制
{"cmd":"cfg","brightness":"80"} // 配置管理
{"cmd":"query","status":"all"}  // 状态查询
{"cmd":"pattern","type":"flow"} // 预设模式
```

### 模块扩展
- 新的输入源 (CAN, Ethernet等)
- 新的执行模块 (音频, 显示等)
- 新的指令类型 (配置, 查询等)

## 🎯 **快速开始**

1. **硬件连接**: 确保CH423S和STM32正确连接
2. **烧录固件**: 编译并烧录到STM32
3. **连接RTT**: 使用J-Link RTT Viewer连接
4. **发送指令**: 输入JSON指令测试LED控制
5. **观察效果**: 验证LED响应和控制效果

### 推荐测试序列
```json
{"cmd":"led","k01":"1"}    // 测试红色LED
{"cmd":"led","k01":"2"}    // 测试绿色LED
{"cmd":"led","k01":"3"}    // 测试双色LED
{"cmd":"led","k01":"0"}    // 测试关闭LED
{"cmd":"led","k65":"1"}    // 测试K65按钮
```

系统已经过充分测试和优化，具备稳定可靠的LED控制能力！🎉
