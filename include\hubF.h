/**
 * @file hubF.h
 * @brief 指令处理中心模块头文件
 * @details 负责接收、解析和分发各种JSON指令
 */

#pragma once

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// ==================== 指令类型定义 ====================

/**
 * @brief 指令类型枚举
 */
typedef enum {
    CMD_LED,           /**< LED控制指令 */
    CMD_BRIGHTNESS,    /**< 亮度控制指令 */
    CMD_UNKNOWN        /**< 未知指令 */
} CommandType_t;

// ==================== 核心API ====================

/**
 * @brief 处理JSON指令
 * @param jsonCommand JSON格式的指令字符串
 * @details 支持的格式: {"cmd":"led","k01":"1"}
 */
void hubProcessCommand(const char* jsonCommand);

/**
 * @brief 初始化指令处理中心
 * @return true 初始化成功
 * @return false 初始化失败
 */
bool hubInit(void);

/**
 * @brief 获取指令处理中心初始化状态
 * @return true 已初始化
 * @return false 未初始化
 */
bool hubIsInitialized(void);

#ifdef __cplusplus
}
#endif