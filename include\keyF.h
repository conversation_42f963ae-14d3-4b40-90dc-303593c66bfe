/**
 * @file keyF.h
 * @brief 键盘扫描模块头文件
 * @details 8x8矩阵键盘扫描，支持64个按键 + 1个独立按键K65(PA0+PB11) + 1个独立按键K65
 */

#ifndef KEYF_H
#define KEYF_H

#include "Arduino.h"
#include <stdint.h>
#include <stdbool.h>

// 键盘矩阵尺寸
#define KEY_ROWS    8    // 行数
#define KEY_COLS    8    // 列数

// 防抖参数
#define KEY_DEBOUNCE_TIME   2    // 防抖次数（2次扫描周期 = 20ms）

/**
 * @brief 按键事件类型
 */
typedef enum {
    KEY_EVENT_PRESS,     // 按键按下
    KEY_EVENT_RELEASE    // 按键释放
} KeyEvent_t;

/**
 * @brief 按键事件回调函数类型
 * @param key_id 按键ID (1-65, 0表示无效)
 * @param event 事件类型
 */
typedef void (*KeyCallback_t)(uint8_t key_id, KeyEvent_t event);

/**
 * @brief 按键映射结构
 */
typedef struct {
    uint8_t row;
    uint8_t col;
    uint8_t key_id;  // 0表示无效按键
} KeyMapping_t;

/**
 * @brief 初始化键盘模块
 * @return true 初始化成功
 * @return false 初始化失败
 */
bool keyInit(void);

/**
 * @brief 注册按键事件回调函数
 * @param callback 回调函数指针
 */
void keyRegisterCallback(KeyCallback_t callback);

/**
 * @brief 键盘扫描任务
 * @note 需要定期调用，建议10ms间隔
 */
void keyScanTask(void);

/**
 * @brief 查询指定按键是否按下
 * @param row 行号 (0-7)
 * @param col 列号 (0-7)
 * @return true 按键按下
 * @return false 按键未按下
 */
bool keyIsPressed(uint8_t row, uint8_t col);

/**
 * @brief 获取当前按下的按键数量
 * @return uint8_t 按下的按键数量
 */
uint8_t keyGetPressedCount(void);

/**
 * @brief 获取键盘模块初始化状态
 * @return true 已初始化
 * @return false 未初始化
 */
bool keyIsInitialized(void);

/**
 * @brief 根据行列坐标获取按键ID
 * @param row 行号 (0-7)
 * @param col 列号 (0-7)
 * @return uint8_t 按键ID (1-65, 0表示无效)
 */
uint8_t keyGetKeyId(uint8_t row, uint8_t col);

/**
 * @brief 键盘模块任务
 * @note 包含键盘扫描和事件处理，需要定期调用
 */
void keyModuleTask(void);

/**
 * @brief 设置UART模块回调函数
 * @param addCallback 添加按键回调函数
 * @param removeCallback 移除按键回调函数
 */
void keySetUartCallbacks(void (*addCallback)(uint8_t), void (*removeCallback)(uint8_t));

/**
 * @brief 测试K65独立按键 (调试用)
 */
void keyTestK65(void);

/**
 * @brief 测试K57-K64按键映射 (调试用)
 */
void keyTestProblemKeys(void);

#endif // KEYF_H
