/**
 * @file keyF.cpp
 * @brief 键盘扫描模块实现
 * @details 8x8矩阵键盘扫描，支持64个按键
 */

#include "keyF.h"
#include "logF.h"

#define TAG "KEY"

// 引脚定义
// 列引脚（输入+上拉）
static const uint8_t col_pins[KEY_COLS] = {
    PA0, PA1, PA2, PA3, PA4, PA5, PA8, PB15
};

// 行引脚（输出）
static const uint8_t row_pins[KEY_ROWS] = {
    PB3, PB4, PB5, PB6, PB7, PB8, PB9, PB10
};

// K65独立按键引脚定义 (根据图纸 - 双端口配置)
#define K65_OUTPUT_PIN PA12  // K65输出引脚 (提供高电平)
#define K65_INPUT_PIN  PA11  // K65输入引脚 (检测按键状态)

// 键盘状态变量
static bool key_current[KEY_ROWS][KEY_COLS];      // 当前扫描状态
static bool key_stable[KEY_ROWS][KEY_COLS];       // 稳定状态（防抖后）
static uint8_t debounce_cnt[KEY_ROWS][KEY_COLS];  // 防抖计数器

// K65独立按键状态变量 (单引脚按键)
static bool k65_current = false;                  // K65当前状态
static bool k65_stable = false;                   // K65稳定状态
static uint8_t k65_debounce_cnt = 0;              // K65防抖计数器



// 模块状态
static bool key_initialized = false;
static KeyCallback_t key_callback = NULL;

// UART模块回调函数指针
static void (*uart_add_callback)(uint8_t) = NULL;
static void (*uart_remove_callback)(uint8_t) = NULL;

// 按键坐标到ID的映射表（8x8矩阵）
static const KeyMapping_t key_mapping[] = {
    // 第一行 (K1-K8)
    {0, 0, 1},   // K1
    {0, 1, 2},   // K2
    {0, 2, 3},   // K3
    {0, 3, 4},   // K4
    {0, 4, 5},   // K5
    {0, 5, 6},   // K6
    {0, 6, 7},   // K7
    {0, 7, 8},   // K8
    
    // 第二行 (K9-K16)
    {1, 0, 9},   // K9
    {1, 1, 10},  // K10
    {1, 2, 11},  // K11
    {1, 3, 12},  // K12
    {1, 4, 13},  // K13
    {1, 5, 14},  // K14
    {1, 6, 15},  // K15
    {1, 7, 16},  // K16
    
    // 第三行 (K17-K24)
    {2, 0, 17},  // K17
    {2, 1, 18},  // K18
    {2, 2, 19},  // K19
    {2, 3, 20},  // K20
    {2, 4, 21},  // K21
    {2, 5, 22},  // K22
    {2, 6, 23},  // K23
    {2, 7, 24},  // K24
    
    // 第四行 (K25-K32)
    {3, 0, 25},  // K25
    {3, 1, 26},  // K26
    {3, 2, 27},  // K27
    {3, 3, 28},  // K28
    {3, 4, 29},  // K29
    {3, 5, 30},  // K30
    {3, 6, 31},  // K31
    {3, 7, 32},  // K32
    
    // 第五行 (K33-K40)
    {4, 0, 33},  // K33
    {4, 1, 34},  // K34
    {4, 2, 35},  // K35
    {4, 3, 36},  // K36
    {4, 4, 37},  // K37
    {4, 5, 38},  // K38
    {4, 6, 39},  // K39
    {4, 7, 40},  // K40
    
    // 第六行 (K41-K48)
    {5, 0, 41},  // K41
    {5, 1, 42},  // K42
    {5, 2, 43},  // K43
    {5, 3, 44},  // K44
    {5, 4, 45},  // K45
    {5, 5, 46},  // K46
    {5, 6, 47},  // K47
    {5, 7, 48},  // K48
    
    // 第七行 (K49-K56)
    {6, 0, 49},  // K49
    {6, 1, 50},  // K50
    {6, 2, 51},  // K51
    {6, 3, 52},  // K52
    {6, 4, 53},  // K53
    {6, 5, 54},  // K54
    {6, 6, 55},  // K55
    {6, 7, 56},  // K56
    
    // 第八行 (K57-K64)
    {7, 0, 57},  // K57
    {7, 1, 58},  // K58
    {7, 2, 59},  // K59
    {7, 3, 60},  // K60
    {7, 4, 61},  // K61
    {7, 5, 62},  // K62
    {7, 6, 63},  // K63
    {7, 7, 64},  // K64
};

/**
 * @brief 根据行列坐标获取按键ID
 */
static uint8_t getKeyIdFromCoord(uint8_t row, uint8_t col) {
    for (int i = 0; i < sizeof(key_mapping)/sizeof(KeyMapping_t); i++) {
        if (key_mapping[i].row == row && key_mapping[i].col == col) {
            return key_mapping[i].key_id;
        }
    }
    return 0;  // 未找到映射，返回无效
}

/**
 * @brief 测试问题行的引脚功能
 */
static void testProblemRowPins(void) {
    LOG_I(TAG, "开始测试问题行引脚...");

    // 测试最后一行 (PB10)
    LOG_I(TAG, "测试最后一行引脚 PB10");
    digitalWrite(row_pins[7], LOW);
    delay(100);
    digitalWrite(row_pins[7], HIGH);

    LOG_I(TAG, "问题行引脚测试完成");
}

/**
 * @brief 初始化K65独立按键GPIO
 */
static void keyInitK65Gpio(void) {
    // 配置PA11为输出，提供高电平信号源
    pinMode(K65_OUTPUT_PIN, OUTPUT);
    digitalWrite(K65_OUTPUT_PIN, HIGH);

    // 配置PA12为输入+软件下拉，检测按键状态
    pinMode(K65_INPUT_PIN, INPUT_PULLDOWN);

    // 短暂延时后读取初始状态
    delay(10);
    bool initial_input_state = digitalRead(K65_INPUT_PIN);

    // 初始化K65状态
    k65_current = false;
    k65_stable = false;
    k65_debounce_cnt = 0;

    LOG_I(TAG, "K65独立按键初始化完成");
    LOG_I(TAG, "PA12: 输出HIGH (信号源)");
    LOG_I(TAG, "PA11: 输入+软件下拉 (检测端)");
    LOG_I(TAG, "PA11初始状态: %s", initial_input_state ? "HIGH(按下)" : "LOW(释放)");
    LOG_I(TAG, "工作原理: 按键按下时PA12的HIGH通过按键连接到PA11");
}

/**
 * @brief 初始化GPIO引脚
 */
static void keyInitGpio(void)
{
    // 初始化列引脚为输入+上拉
    for (uint8_t i = 0; i < KEY_COLS; i++) {
        pinMode(col_pins[i], INPUT_PULLUP);
    }

    // 初始化行引脚为输出+高电平
    for (uint8_t i = 0; i < KEY_ROWS; i++) {
        pinMode(row_pins[i], OUTPUT);
        digitalWrite(row_pins[i], HIGH);
    }

    // 初始化K65独立按键
    keyInitK65Gpio();

    LOG_I(TAG, "GPIO初始化完成");
    LOG_I(TAG, "列引脚: PA0,PA1,PA2,PA3,PA4,PA5,PA8,PB15");
    LOG_I(TAG, "行引脚: PB3,PB4,PB5,PB6,PB7,PB8,PB9,PB10");
    LOG_I(TAG, "串口引脚: PA9(RX), PA10(TX)");

    // 测试问题行引脚
    testProblemRowPins();
}

/**
 * @brief 初始化键盘状态
 */
static void keyInitState(void)
{
    // 清零所有状态
    for (uint8_t row = 0; row < KEY_ROWS; row++) {
        for (uint8_t col = 0; col < KEY_COLS; col++) {
            key_current[row][col] = false;
            key_stable[row][col] = false;
            debounce_cnt[row][col] = 0;
        }
    }

    LOG_I(TAG, "键盘状态初始化完成");
}

/**
 * @brief 扫描K65独立按键 (PA12输出HIGH, PA11输入检测)
 */
static void keyScanK65(void) {
    // 读取PA12和PA11的状态
    bool pa12_state = digitalRead(K65_OUTPUT_PIN);
    bool input_state = digitalRead(K65_INPUT_PIN);

    // PA11为高电平表示按键按下（PA12的高电平通过按键传递到PA11）
    k65_current = (input_state == HIGH);

    // 详细调试信息
    static bool last_key_state = false;
    static uint32_t last_debug_time = 0;
    uint32_t current_time = millis();

    // 状态变化时输出调试信息
    if (k65_current != last_key_state) {
        LOG_D(TAG, "K65按键状态变化: %s → %s (PA11:%s, PA12:%s)",
              last_key_state ? "按下" : "释放",
              k65_current ? "按下" : "释放",
              input_state ? "HIGH" : "LOW",
              pa12_state ? "HIGH" : "LOW");
        last_key_state = k65_current;
    }

    // 每3秒输出一次当前状态（用于调试）
    if (current_time - last_debug_time > 3000) {
        LOG_D(TAG, "K65状态监控: PA11=%s, PA12=%s, 按键=%s, 稳定=%s",
              input_state ? "HIGH" : "LOW",
              pa12_state ? "HIGH" : "LOW",
              k65_current ? "按下" : "释放",
              k65_stable ? "按下" : "释放");
        last_debug_time = current_time;
    }
}

/**
 * @brief 扫描单行按键
 * @param row 行号
 */
static void keyScanRow(uint8_t row)
{
    // 拉低当前行
    digitalWrite(row_pins[row], LOW);

    // 短暂延时，确保信号稳定
    delayMicroseconds(10);

    // 读取所有列的状态
    bool row_has_activity = false;
    for (uint8_t col = 0; col < KEY_COLS; col++) {
        // 读取列状态（低电平表示按键按下）
        bool pressed = (digitalRead(col_pins[col]) == LOW);
        key_current[row][col] = pressed;

        if (pressed) {
            row_has_activity = true;
        }
    }

    // 恢复当前行为高电平
    digitalWrite(row_pins[row], HIGH);

    // 调试：记录第7行和第8行的活动
    // if ((row == 7 || row == 8) && row_has_activity) {
    //     LOG_I(TAG, "检测到行%d有按键活动 (引脚:%s)", row,
    //           row == 7 ? "PB10" : "PB0");
    // }
}

/**
 * @brief 处理防抖和事件触发
 */
static void keyProcessDebounce(void)
{
    for (uint8_t row = 0; row < KEY_ROWS; row++) {
        for (uint8_t col = 0; col < KEY_COLS; col++) {
            bool current = key_current[row][col];
            bool stable = key_stable[row][col];

            if (current == stable) {
                // 状态一致，重置计数器
                debounce_cnt[row][col] = 0;
            } else {
                // 状态不一致，增加计数器
                debounce_cnt[row][col]++;

                if (debounce_cnt[row][col] >= KEY_DEBOUNCE_TIME) {
                    // 防抖时间到，更新稳定状态
                    key_stable[row][col] = current;
                    debounce_cnt[row][col] = 0;

                    // 获取按键ID
                    uint8_t key_id = getKeyIdFromCoord(row, col);

                    // 打印坐标信息（包括无效按键）
                    if (current) {
                        if (key_id > 0) {
                            // 获取行引脚名称
                            const char* pin_names[] = {"PB3", "PB4", "PB5", "PB6", "PB7", "PB8", "PB9", "PB10"};
                            const char* pin_name = (row < 8) ? pin_names[row] : "UNKNOWN";
                            LOG_I(TAG, "按键按下: K%d (行%d列%d, 引脚:%s)", key_id, row, col, pin_name);
                        } else {
                            LOG_W(TAG, "无效按键: 行%d列%d (已忽略)", row, col);
                        }
                    } else {
                        if (key_id > 0) {
                            LOG_I(TAG, "按键释放: K%d", key_id);
                        }
                    }

                    // 只处理有效按键
                    if (key_id > 0 && key_callback != NULL) {
                        KeyEvent_t event = current ? KEY_EVENT_PRESS : KEY_EVENT_RELEASE;
                        key_callback(key_id, event);
                    }
                }
            }
        }
    }

    // 处理K65独立按键的防抖
    if (k65_current == k65_stable) {
        // 状态一致，重置计数器
        k65_debounce_cnt = 0;
    } else {
        // 状态不一致，增加计数器
        k65_debounce_cnt++;

        if (k65_debounce_cnt >= KEY_DEBOUNCE_TIME) {
            // 防抖时间到，更新稳定状态
            k65_stable = k65_current;
            k65_debounce_cnt = 0;

            // 打印K65按键信息
            if (k65_current) {
                LOG_I(TAG, "按键按下: K65 (独立按键, PA12→PA11双端口模式)");
            } else {
                LOG_I(TAG, "按键释放: K65 (独立按键, PA12→PA11双端口模式)");
            }

            // 触发K65按键事件
            if (key_callback != NULL) {
                KeyEvent_t event = k65_current ? KEY_EVENT_PRESS : KEY_EVENT_RELEASE;
                key_callback(65, event);  // K65的ID是65
            }
        }
    }
}

// ==================== 公共API函数 ====================

/**
 * @brief 初始化键盘模块
 */
bool keyInit(void)
{
    if (key_initialized) {
        LOG_W(TAG, "键盘模块已经初始化");
        return true;
    }

    LOG_I(TAG, "开始初始化键盘模块");
    LOG_I(TAG, "矩阵尺寸: %dx%d", KEY_ROWS, KEY_COLS);

    // 初始化GPIO
    keyInitGpio();

    // 初始化状态
    keyInitState();

    // 标记为已初始化
    key_initialized = true;

    LOG_I(TAG, "键盘模块初始化完成");
    return true;
}

/**
 * @brief 注册按键事件回调函数
 */
void keyRegisterCallback(KeyCallback_t callback)
{
    key_callback = callback;
    LOG_I(TAG, "按键事件回调函数已注册");
}

/**
 * @brief 键盘扫描任务
 */
void keyScanTask(void)
{
    if (!key_initialized) {
        return;
    }

    // 扫描所有行
    for (uint8_t row = 0; row < KEY_ROWS; row++) {
        keyScanRow(row);
    }

    // 扫描K65独立按键
    keyScanK65();

    // 处理防抖和事件
    keyProcessDebounce();
}

/**
 * @brief 查询指定按键是否按下
 */
bool keyIsPressed(uint8_t row, uint8_t col)
{
    if (!key_initialized || row >= KEY_ROWS || col >= KEY_COLS) {
        return false;
    }

    return key_stable[row][col];
}

/**
 * @brief 获取当前按下的按键数量
 */
uint8_t keyGetPressedCount(void)
{
    if (!key_initialized) {
        return 0;
    }

    uint8_t count = 0;
    for (uint8_t row = 0; row < KEY_ROWS; row++) {
        for (uint8_t col = 0; col < KEY_COLS; col++) {
            if (key_stable[row][col]) {
                count++;
            }
        }
    }

    return count;
}

/**
 * @brief 获取键盘模块初始化状态
 */
bool keyIsInitialized(void)
{
    return key_initialized;
}

/**
 * @brief 根据行列坐标获取按键ID
 */
uint8_t keyGetKeyId(uint8_t row, uint8_t col)
{
    return getKeyIdFromCoord(row, col);
}

/**
 * @brief 内部按键事件处理函数
 */
static void internalKeyEventHandler(uint8_t key_id, KeyEvent_t event)
{
    if (key_id == 0) {
        // 无效按键，忽略
        return;
    }

    if (event == KEY_EVENT_PRESS) {
        // 坐标信息已在防抖处理中打印，这里只处理UART回调
        if (uart_add_callback != NULL) {
            uart_add_callback(key_id);
        }
    } else if (event == KEY_EVENT_RELEASE) {
        if (uart_remove_callback != NULL) {
            uart_remove_callback(key_id);
        }
    }
}

/**
 * @brief 键盘模块任务
 */
void keyModuleTask(void)
{
    static bool firstRun = true;

    // 首次运行时初始化键盘模块
    if (firstRun) {
        if (keyInit()) {
            // 注册内部事件处理函数
            keyRegisterCallback(internalKeyEventHandler);
            LOG_I(TAG, "键盘模块任务初始化成功");
        } else {
            LOG_E(TAG, "键盘模块任务初始化失败");
        }
        firstRun = false;
        return;
    }

    // 执行键盘扫描
    keyScanTask();
}

/**
 * @brief 设置UART模块回调函数
 */
void keySetUartCallbacks(void (*addCallback)(uint8_t), void (*removeCallback)(uint8_t))
{
    uart_add_callback = addCallback;
    uart_remove_callback = removeCallback;
    LOG_I(TAG, "UART回调函数已设置");
}

/**
 * @brief 专门测试K65按键的函数
 */
void keyTestK65(void) {
    LOG_I(TAG, "开始测试K65独立按键...");
    LOG_I(TAG, "K65配置: PA12输出HIGH, PA11输入+软件下拉");

    // 读取当前引脚状态
    bool output_state = digitalRead(K65_OUTPUT_PIN);
    bool input_state = digitalRead(K65_INPUT_PIN);
    LOG_I(TAG, "K65当前引脚状态:");
    LOG_I(TAG, "  PA12(输出): %s", output_state ? "HIGH" : "LOW");
    LOG_I(TAG, "  PA11(输入): %s", input_state ? "HIGH" : "LOW");
    LOG_I(TAG, "K65当前扫描状态: %s", k65_current ? "按下" : "释放");
    LOG_I(TAG, "K65当前稳定状态: %s", k65_stable ? "按下" : "释放");
    LOG_I(TAG, "K65防抖计数器: %d", k65_debounce_cnt);

    LOG_I(TAG, "请按下K65按键进行测试...");
    LOG_I(TAG, "预期行为: 按下时PA11从LOW变为HIGH");
    LOG_I(TAG, "工作原理: PA12(HIGH) → 按键 → PA11(检测)");
}

/**
 * @brief 专门测试K57-K64按键的函数
 */
void keyTestProblemKeys(void) {
    LOG_I(TAG, "开始测试K57-K64按键映射...");

    // K57-K64的映射信息
    const struct {
        uint8_t key_id;
        uint8_t row;
        uint8_t col;
        const char* pin_name;
    } problem_keys[] = {
        {57, 7, 0, "PB10"},  // K57
        {58, 7, 1, "PB10"},  // K58
        {59, 7, 2, "PB10"},  // K59
        {60, 7, 3, "PB10"},  // K60
        {61, 7, 4, "PB10"},  // K61
        {62, 7, 5, "PB10"},  // K62
        {63, 7, 6, "PB10"},  // K63
        {64, 7, 7, "PB10"},  // K64
    };

    for (int i = 0; i < 8; i++) {
        LOG_I(TAG, "K%d: 行%d列%d (引脚%s)",
              problem_keys[i].key_id,
              problem_keys[i].row,
              problem_keys[i].col,
              problem_keys[i].pin_name);
    }

    LOG_I(TAG, "请按下K57-K64中的任意按键进行测试...");
}
